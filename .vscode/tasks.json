{"version": "2.0.0", "tasks": [{"label": "typecheck", "type": "shell", "command": "bun", "args": ["run", "typecheck"], "isBackground": false, "problemMatcher": ["$tsc"], "group": "build"}, {"label": "typecheck", "type": "shell", "command": "bun", "args": ["run", "typecheck"], "isBackground": false, "problemMatcher": ["$tsc"], "group": "build"}, {"label": "lint", "type": "shell", "command": "bun", "args": ["run", "lint"], "isBackground": false, "problemMatcher": ["$eslint-stylish"], "group": "build"}, {"label": "typecheck", "type": "shell", "command": "bun", "args": ["run", "typecheck"], "isBackground": false, "problemMatcher": ["$tsc"], "group": "build"}, {"label": "typecheck", "type": "shell", "command": "bun", "args": ["run", "typecheck"], "isBackground": false, "problemMatcher": ["$tsc"], "group": "build"}]}