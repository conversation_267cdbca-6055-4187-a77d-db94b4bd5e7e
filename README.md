# 多云CDN管理系统

## 项目概述

这是一个现代化的 React Router SPA 应用程序，展示了如何使用最新的 Web 技术创建专业的多云CDN管理界面。项目采用模拟数据展示多云CDN管理的核心功能，提供了完整的导航体系和数据可视化界面。

## 预览截图

![多云CDN管理系统预览](docs/images/mcdn-preview.png)

## 核心功能

- **服务概览** - CDN服务的实时数据仪表板，包含流量、带宽、请求数等关键指标
- **数据可视化** - 基于 Recharts 的交互式图表，支持趋势分析和时间范围切换
- **多云域名管理** - 支持多个云服务商的域名配置管理（模拟数据）
- **厂商管理** - 云服务提供商的统一管理界面
- *权限管理** - 用户授权和 API Key 管理功能
- **深色模式支持** - 基于 shadcn/ui 的主题切换功能
- **响应式设计** - 适配桌面端和移动端的自适应布局
- **可折叠侧边栏** - 优化空间利用的导航结构

## 技术栈

- **React Router v7** - 现代化的 React 路由解决方案（SPA 模式）
- **shadcn/ui** - 基于 Radix UI 的组件系统
- **Tailwind CSS 4.x** - 通过 Vite 插件集成的原子化 CSS 框架
- **TypeScript** - 类型安全的开发体验
- **Recharts** - React 数据可视化图表库
- **@tanstack/react-table** - 强大的表格数据管理
- **Lucide React** - 现代化的图标库
- **Bun** - 高性能的 JavaScript 运行时和包管理器

## 快速开始

### 安装依赖

```bash
bun install
```

### 开发环境

启动带热更新的开发服务器：

```bash
bun run dev
```

应用将在 `http://localhost:5173` 可用。

## 生产构建

创建生产构建版本：

```bash
bun run build
```

## 部署

### Docker 部署

针对 SPA 应用优化的 Docker 构建，使用 Nginx 提供静态文件服务：

```bash
docker build -t mcdn-web .

# 运行容器
docker run -p 8080:80 mcdn-web
```

应用将在 `http://localhost:8080` 可用。

推荐的容器化部署平台：

- AWS ECS
- Google Cloud Run  
- Azure Container Apps
- Digital Ocean App Platform
- Fly.io
- Railway

### 静态文件部署

由于项目使用 SPA 模式（`ssr: false`），也可以将构建输出部署到静态文件服务：

```bash
# 构建静态文件
bun run build

# build/client/ 目录包含所有静态资源，可直接部署到：
# - Vercel
# - Netlify  
# - GitHub Pages
# - AWS S3 + CloudFront
```

## 样式设计

本模板已配置 [Tailwind CSS](https://tailwindcss.com/)，提供简洁的默认样式体验。您可以使用任何喜欢的 CSS 框架。

## 项目结构

```text
app/
├── components/              # 可复用的UI组件
│   ├── ui/                 # shadcn/ui 基础组件
│   │   ├── base/           # 核心 UI 组件
│   │   ├── brand/          # 品牌定制组件  
│   │   └── extras/         # 扩展功能组件
│   ├── domains/            # 域名管理相关组件
│   ├── vendors/            # 厂商管理相关组件
│   ├── app-layout.tsx      # 应用主布局
│   ├── app-sidebar.tsx     # 导航侧边栏
│   └── top-nav.tsx         # 顶部导航栏
├── routes/                 # 路由页面组件
│   ├── home.tsx            # 服务概览首页
│   ├── about.tsx           # 关于页面
│   ├── vendors.tsx         # 厂商管理页面
│   ├── domains/            # 域名管理相关路由
│   │   ├── domains.tsx     # 域名列表页
│   │   └── domains.$domain.tsx  # 域名详情页
│   └── auth/               # 权限管理路由
│       ├── users.tsx       # 用户管理
│       └── api-keys.tsx    # API密钥管理
├── types/                  # TypeScript 类型定义
│   ├── domain.ts           # 域名相关类型
│   ├── cloud.ts            # 云服务商类型
│   └── auth.ts             # 权限管理类型
├── lib/                    # 工具库
│   ├── utils.ts            # 通用工具函数
│   └── time-range.ts       # 时间范围处理
├── hooks/                  # 自定义 React Hooks
│   └── use-mobile.ts       # 移动端检测
├── root.tsx                # 应用根组件
├── routes.ts               # 集中式路由配置
└── app.css                 # 全局样式定义
```

## 数据说明

本项目使用模拟数据进行演示：

- **CDN 流量数据** - 基于时间序列的模拟流量、带宽和请求数数据
- **域名配置** - 多个云服务商的域名管理示例数据
- **厂商信息** - 主流云服务商（阿里云、腾讯云、火山引擎等）的基础信息
- **用户权限** - 演示用的用户角色和 API Key 管理

真实部署时，可以将这些模拟数据替换为实际的 API 接口调用。

---

使用 React Router v7、shadcn/ui 和 Bun 构建 ❤️
