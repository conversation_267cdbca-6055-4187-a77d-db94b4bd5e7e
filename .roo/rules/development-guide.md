# 多云CDN管理系统 - 开发指南

## 关键开发模式

### 路由配置

- **文件位置：** `app/routes.ts` - 使用 React Router v7 的新配置格式
- **添加新路由：** 使用 `route('path', 'routes/file.tsx')` 函数
- **开发环境特殊路由：** `.well-known` 路由仅在开发模式启用

### shadcn/ui 集成

- **配置文件：** `components.json` - 使用 "new-york" 风格，启用 CSS 变量
- **组件别名：** `~/components/ui` - 通过 tsconfig paths 映射
- **自定义组件：** 扩展 shadcn/ui 基础组件，如 `cloud-icon.tsx`、`copy-button.tsx`

### 数据管理模式

- **状态管理：** 使用 React `useState` 进行本地状态管理
- **模拟数据：** 页面组件内定义 mock 数据（如 `domains.tsx` 中的 `mockDomains`）
- **类型定义：** TypeScript 类型集中在 `app/types/` 目录

### 组件组织原则

```
app/components/
├── ui/ - shadcn/ui 基础组件
├── domains/ - 业务域特定组件（domains-table.tsx）
├── vendors/ - 厂商管理相关组件
├── app-layout.tsx - 应用级布局组件
└── app-sidebar.tsx - 导航侧边栏
```

## 关键开发工作流

### 本地开发

```bash
bun run dev  # 启动开发服务器（端口 5173）
bun run typecheck  # TypeScript 类型检查
bun run lint  # ESLint 代码检查
```

### 构建和部署

```bash
bun run build  # 生产构建到 build/ 目录
bun run start  # 启动生产服务器
docker build -t my-app .  # Docker 容器化部署
```

## AI 开发助手指南

### 代码生成模式

1. **组件创建：** 当创建新组件时，遵循现有的组件结构和样式约定
2. **路由添加：** 在添加新路由时，确保同时更新 `app/routes.ts` 和侧边栏导航
3. **类型定义：** 在 `app/types/` 目录中添加新的 TypeScript 类型定义

### 最佳实践

1. **响应式设计：** 使用 Tailwind CSS 的响应式类确保在不同设备上的兼容性
2. **可访问性：** 遵循 WCAG 标准，确保所有组件都具有适当的 ARIA 属性
3. **性能优化：** 使用 React.memo 和 useMemo 优化组件性能
4. **错误处理：** 实现适当的错误边界和错误处理机制

### 命令约定

- **代码解释：** 使用 `// 解释:` 注释来说明复杂逻辑
- **TODO 标记：** 使用 `// TODO(ai):` 标记需要进一步处理的代码
- **代码块：** 在生成代码时，使用适当的代码块格式
