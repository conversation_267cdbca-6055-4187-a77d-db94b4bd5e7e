# 常见开发任务

## 添加新的云服务商

### 步骤流程

1. **更新类型定义：** 在 `app/types/cloud.ts` 中的 `CloudProvider` 类型添加新的服务商
2. **添加图标支持：** 在 `CloudIconWithText` 组件中添加新服务商的图标和显示逻辑
3. **更新模拟数据：** 在相关页面组件中更新 mock 数据，包含新的云服务商
4. **测试集成：** 确保新服务商在所有相关界面正常显示

### 代码示例

```typescript
// app/types/cloud.ts
export type CloudProvider = 
  | 'alibaba-cloud' 
  | 'tencent-cloud' 
  | 'volcengine' 
  | 'aws' 
  | 'azure' 
  | 'google-cloud'
  | 'new-provider'; // 添加新服务商
```

## 创建新的管理页面

### 开发流程

1. **创建页面组件：** 在 `app/routes/` 目录下创建新的页面组件文件
2. **注册路由：** 在 `app/routes.ts` 中添加新路由的配置
3. **更新导航：** 在 `app-sidebar.tsx` 中添加对应的导航菜单项
4. **创建业务组件：** 如需要，创建对应的表格或表单组件

### 文件结构示例

```
app/routes/new-feature.tsx          # 新页面组件
app/components/new-feature/         # 业务组件目录
├── new-feature-table.tsx          # 表格组件
└── new-feature-form.tsx           # 表单组件
```

## 扩展 UI 组件

### 组件开发原则

- **基于 shadcn/ui：** 使用 shadcn/ui 组件作为基础进行扩展
- **遵循样式约定：** 使用项目特定的 Tailwind 类名约定
- **样式合并：** 使用 `cn()` 工具函数合并样式类
- **类型安全：** 为所有组件定义完整的 TypeScript 接口

### 组件扩展示例

```typescript
import { cn } from "~/lib/utils";
import { Button } from "~/components/ui/button";

interface CustomButtonProps {
  variant?: "primary" | "secondary";
  size?: "sm" | "md" | "lg";
  children: React.ReactNode;
}

export function CustomButton({ variant = "primary", size = "md", children, ...props }: CustomButtonProps) {
  return (
    <Button
      className={cn(
        "font-medium",
        variant === "primary" && "bg-lilith-primary text-white",
        variant === "secondary" && "bg-lilith-secondary text-lilith-foreground",
        size === "sm" && "px-3 py-1 text-sm",
        size === "md" && "px-4 py-2",
        size === "lg" && "px-6 py-3 text-lg"
      )}
      {...props}
    >
      {children}
    </Button>
  );
}
```

## 数据表格开发

### 表格组件模式

- **使用 @tanstack/react-table：** 用于复杂表格功能（排序、筛选、分页）
- **操作回调模式：** 通过回调函数处理用户操作（`onEdit`、`onDelete`、`onToggle`）
- **状态提升：** 表格数据在父组件管理，通过 props 传递

### 表格开发模板

```typescript
interface TableData {
  id: string;
  name: string;
  status: 'active' | 'inactive';
  // 其他字段...
}

interface DataTableProps {
  data: TableData[];
  onEdit: (item: TableData) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string) => void;
}

export function DataTable({ data, onEdit, onDelete, onToggleStatus }: DataTableProps) {
  // 表格实现...
}
```

## 状态管理模式

### 本地状态管理

- **useState：** 用于组件内部状态
- **useEffect：** 处理副作用和数据获取
- **自定义 Hooks：** 封装复杂的状态逻辑

### 状态管理示例

```typescript
function useDataManagement() {
  const [data, setData] = useState<TableData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleEdit = useCallback((item: TableData) => {
    // 编辑逻辑
  }, []);

  const handleDelete = useCallback((id: string) => {
    setData(prev => prev.filter(item => item.id !== id));
  }, []);

  return {
    data,
    loading,
    error,
    handleEdit,
    handleDelete
  };
}
```

## 错误处理和调试

### 错误处理策略

- **错误边界：** 实现 React 错误边界组件
- **类型检查：** 使用 TypeScript 进行编译时检查
- **运行时验证：** 对外部数据进行验证

### 调试技巧

- **开发工具：** 使用 React Developer Tools
- **日志记录：** 在关键位置添加调试日志
- **断点调试：** 使用浏览器开发者工具进行调试
