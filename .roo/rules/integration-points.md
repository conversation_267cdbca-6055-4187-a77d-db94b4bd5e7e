# 关键集成点

## 云服务商抽象

### 类型定义

- **文件位置：** `app/types/cloud.ts` - 定义 CloudProvider 类型
- **图标组件：** `CloudIconWithText` 组件处理不同云服务商的视觉展示
- **厂商特定逻辑：** 在组件层面处理不同云服务商的差异化配置

### 支持的云服务商

- 阿里云 (Alibaba Cloud)
- 腾讯云 (Tencent Cloud)
- 火山引擎 (Volcengine)
- AWS
- Azure
- Google Cloud

## 业务逻辑模式

### 域名管理

- **多云流量调度：** 支持开关控制
- **HTTPS 配置：** SSL 证书管理
- **状态管理：** 实时状态监控
- **权限控制：** 基于角色的操作权限

### 数据模型

- **域名实体：** Domain 类型定义
- **云服务商实体：** CloudProvider 类型定义
- **实时更新：** lastUpdateTime 字段追踪

## 组件集成

### 表格组件集成

- **@tanstack/react-table：** 复杂表格功能
- **排序和筛选：** 内置数据操作
- **分页控制：** 大数据集处理
- **操作列：** 编辑、删除、状态切换

### UI 组件集成

- **shadcn/ui 基础组件：** Button, Card, Table 等
- **自定义扩展组件：** CloudIcon, CopyButton 等
- **响应式布局：** 移动端适配

## API 集成模式

### 数据获取

- **Mock 数据：** 开发阶段使用模拟数据
- **类型安全：** TypeScript 接口定义
- **错误处理：** 统一错误处理机制

### 状态同步

- **本地状态：** React useState 管理
- **数据流：** 单向数据流模式
- **事件处理：** 回调函数模式
