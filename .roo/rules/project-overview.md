# 多云CDN管理系统 - 项目概述

这是一个基于 React Router v7 构建的多云CDN管理系统，专注于云服务商资源的统一管理和流量调度。

## 架构概览

**核心设计理念：** 采用 SPA (Single Page Application) 模式运行，通过 `react-router.config.ts` 中的 `ssr: false` 配置。这是一个前端管理面板，通过模拟数据展示多云CDN管理功能。

**主要组件层次：**

```
App (root.tsx)
├── AppLayout (components/app-layout.tsx) - 提供 SidebarProvider 和整体布局
│   ├── AppSidebar - 可折叠侧边栏导航
│   └── SidebarInset - 主内容区域
└── Route Components (routes/*.tsx) - 页面级组件
```

## 技术栈

- **框架：** React Router v7
- **包管理器：** Bun
- **样式系统：** Tailwind CSS 4.x
- **组件库：** shadcn/ui
- **状态管理：** React useState
- **表格组件：** @tanstack/react-table

## 项目特定约定

### 包管理器和工具链

- **Bun 为主：** 本项目使用 Bun 作为主要包管理器和 JavaScript 运行时
- **命令约定：**
  - 使用 `bun add` 代替 `npm install`
  - 使用 `bun run` 代替 `npm run`
  - 使用 `bunx` 代替 `npx` 执行包命令
  - 使用 `bun install` 安装依赖
- **脚本执行：** 所有 package.json 中的脚本都通过 `bun run <script>` 执行

### 样式系统

- **Tailwind CSS 4.x：** 通过 `@tailwindcss/vite` 插件集成
- **自定义颜色：** 使用 `text-lilith-primary`、`text-lilith-muted-foreground` 等项目特定类名
- **响应式：** 采用 mobile-first 设计，侧边栏在小屏幕自动折叠

### 国际化和本地化

- **中文为主：** 界面文本使用中文，注释和代码使用英文
- **时间格式：** 使用相对时间显示（"3 分钟前"、"刚刚"）

### 数据表格模式

- **@tanstack/react-table：** 用于复杂表格功能（排序、筛选、分页）
- **操作模式：** 通过回调函数处理用户操作（`onToggleTrafficScheduling`、`onEdit`、`onDelete`）
- **状态管理：** 表格数据在父组件管理，通过 props 传递操作函数
