# 常见错误和避免事项

## ESLint 配置错误

### 错误做法

在 ESLint 9.x flat config 中错误地认为 `extends` 语法不被支持，导致：

1. 使用 `js.configs.recommended` 和 `...tseslint.configs.recommended.rules` 的冗长写法
2. 误导性地说 flat config 不支持 `extends`

### 正确做法

ESLint 9.x flat config **完全支持** `extends` 语法，应该使用官方推荐的简洁写法：

```javascript
{
    files: ['**/*.{js,jsx}'],
    plugins: { js },
    extends: ['js/recommended'],  // ✅ 正确：简洁且官方推荐
    rules: { /* 自定义规则 */ }
}
```

### 避免错误的关键

- 总是参考 ESLint 官方文档的最新示例
- 不要基于过时或不准确的信息做假设
- 当用户提供官方示例时，优先采用官方做法
- flat config 与传统配置在 `extends` 语法上是兼容的

## ESLint defineConfig 导入路径错误

### 错误做法

错误地认为 `defineConfig` 应该从 `eslint/define-config` 导入：

```javascript
// ❌ 错误：错误的导入路径
import { defineConfig } from 'eslint/define-config';
```

### 正确做法

在 ESLint 9.x flat config 中，`defineConfig` 应该从 `eslint/config` 导入：

```javascript
// ✅ 正确：正确的导入路径
import { defineConfig } from 'eslint/config';
```

### 避免错误的关键

- 验证导入路径是否正确工作，可以通过创建测试文件来验证
- 参考 ESLint 官方文档和示例
- 当用户指出错误时，应该验证而不是假设

## 包管理器选择错误

### 错误做法

在明确使用 Bun 的项目中，错误地使用 npm/npx 命令：

1. 使用 `npx` 执行包命令，而不是 `bunx`
2. 建议使用 `npm install` 而不是 `bun add`
3. 没有注意到项目根目录存在 `bun.lock` 文件

### 正确做法

在 Bun 项目中应该统一使用 Bun 生态的命令：

```bash
# ✅ 正确：使用 Bun 命令
bun add <package>           # 安装依赖
bun add -D <package>        # 安装开发依赖
bunx <command>              # 执行包命令
bun run <script>            # 运行脚本

# ❌ 错误：在 Bun 项目中使用 npm
npm install <package>
npx <command>
```

### 识别项目包管理器的方法

- 检查根目录的锁文件：`bun.lock`、`package-lock.json`、`yarn.lock`
- 查看 package.json 中的 scripts 和依赖版本格式
- 注意项目文档中的安装和运行说明

## React Router v7 常见错误

### 路由配置错误

```typescript
// ❌ 错误：使用旧版本的路由配置方式
import { createBrowserRouter } from "react-router-dom";

// ✅ 正确：使用 React Router v7 的新配置格式
// 在 app/routes.ts 中使用 route() 函数
import { route } from "@react-router/dev/routes";

export default [
  route("path", "routes/file.tsx")
];
```

### 组件导入错误

```typescript
// ❌ 错误：使用相对路径导入
import { Button } from "../../../components/ui/button";

// ✅ 正确：使用配置的路径别名
import { Button } from "~/components/ui/button";
```

## TypeScript 类型错误

### Props 接口缺失

```typescript
// ❌ 错误：没有定义 Props 类型
function Component(props) {
  return <div>{props.title}</div>;
}

// ✅ 正确：定义完整的 Props 接口
interface ComponentProps {
  title: string;
  onAction?: () => void;
}

function Component({ title, onAction }: ComponentProps) {
  return <div>{title}</div>;
}
```

### 状态类型不明确

```typescript
// ❌ 错误：状态类型不明确
const [data, setData] = useState([]);

// ✅ 正确：明确指定状态类型
const [data, setData] = useState<TableData[]>([]);
```

## Tailwind CSS 样式错误

### 自定义颜色使用错误

```typescript
// ❌ 错误：使用通用颜色类名
<div className="text-blue-500 bg-gray-100">

// ✅ 正确：使用项目特定的颜色变量
<div className="text-lilith-primary bg-lilith-background">
```

### 响应式设计遗漏

```typescript
// ❌ 错误：只考虑桌面端样式
<div className="p-6 text-lg">

// ✅ 正确：包含响应式设计
<div className="p-4 md:p-6 text-base md:text-lg">
```

## 性能优化错误

### 不必要的重新渲染

```typescript
// ❌ 错误：每次渲染都创建新的对象
function Component() {
  const handleClick = () => console.log('clicked');
  return <Button onClick={handleClick} />;
}

// ✅ 正确：使用 useCallback 优化
function Component() {
  const handleClick = useCallback(() => {
    console.log('clicked');
  }, []);
  return <Button onClick={handleClick} />;
}
```

### 缺少 memo 优化

```typescript
// ❌ 错误：纯组件没有使用 memo
function PureComponent({ title }) {
  return <div>{title}</div>;
}

// ✅ 正确：对纯组件使用 memo
const PureComponent = memo(function PureComponent({ title }) {
  return <div>{title}</div>;
});
```

## 错误处理缺失

### 缺少错误边界

```typescript
// ❌ 错误：没有错误处理机制
function App() {
  return <Routes />;
}

// ✅ 正确：包含错误边界
function App() {
  return (
    <ErrorBoundary>
      <Routes />
    </ErrorBoundary>
  );
}
```

## 避免错误的最佳实践

1. **代码审查：** 定期进行代码审查，发现潜在问题
2. **类型检查：** 启用 TypeScript 严格模式
3. **工具配置：** 正确配置 ESLint、Prettier 等工具
4. **文档参考：** 始终参考官方文档的最新版本
5. **测试验证：** 编写测试用例验证功能正确性
