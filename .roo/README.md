# Roo Code 配置目录

这个目录包含了专门为 Roo Code AI 助手优化的配置文件和指令，遵循官网推荐的最佳实践。

## 目录结构

```
.roo/
├── ai/                           # AI助手专用指令
│   └── instructions.md          # Roo Code 工作区指令
├── rules/                       # 工作区级别指令（推荐方式）
│   ├── project-overview.md     # 项目概述和架构
│   └── development-guide.md    # 开发模式和工作流
├── rules-code/                 # 代码模式专用指令
│   └── coding-standards.md     # 编程标准和规范
├── rules-architect/            # 架构师模式专用指令
│   └── architecture-guidelines.md # 架构设计指南
├── rules-debug/                # 调试模式专用指令
│   └── debugging-guide.md      # 调试策略和工具
├── project/                    # 项目文档（保留兼容性）
│   ├── overview.md
│   ├── development.md
│   ├── conventions.md
│   ├── integrations.md
│   └── tasks.md
├── config.json                # Roo Code 配置文件
└── README.md                   # 本文件
```

## 配置说明

### config.json

这个文件包含了 Roo Code 需要了解的项目信息：

- **ai**: AI 助手的相关配置，包括使用的模型和指令文件路径
- **project**: 项目的基本信息，如框架类型、包管理器等
- **architecture**: 项目架构信息，包括主要组件和目录结构
- **commands**: 常用的开发命令
- **conventions**: 项目的编码约定和规范
- **modes**: 模式特定的配置和文件模式限制

### 工作区级别指令

根据官网推荐，使用 `rules/` 目录存放工作区级别的指令：

- **推荐方式：** 目录基础方式 (`.roo/rules/`)
- **文件加载：** 按字母顺序递归加载所有文件
- **优先级：** 目录方式优先于单文件方式

### 模式特定指令

为不同的 AI 模式提供专门的指令：

- `rules-code/` - 代码模式专用指令和编程标准
- `rules-architect/` - 架构师模式专用指令和设计指南
- `rules-debug/` - 调试模式专用指令和调试策略

### AI指令文件

`ai/instructions.md` 文件是专门为 Roo Code 优化的工作区指令，包含了：

- 语言偏好设置（简体中文）
- AI 助手行为指南
- 代码生成原则
- 开发工作流程
- 质量标准要求

## 使用说明

Roo Code 会自动读取这些配置文件来更好地理解和协助开发这个项目。配置文件中的信息将帮助 Roo Code：

1. **理解项目结构：** 通过架构信息了解项目组织方式
2. **遵循编码约定：** 按照项目规范生成代码
3. **提供准确建议：** 基于技术栈提供相关建议
4. **模式特定行为：** 在不同模式下采用相应的工作方式

## 最佳实践

### 指令文件组织

1. **按主题分类：** 将相关指令放在同一个文件中
2. **模式特定：** 为不同模式创建专门的指令目录
3. **层次结构：** 使用清晰的目录层次组织指令

### 配置管理

1. **版本控制：** 将配置文件纳入版本控制
2. **文档同步：** 保持配置和文档的同步更新
3. **团队共享：** 确保团队成员使用一致的配置

### 指令编写

1. **清晰具体：** 编写明确、可执行的指令
2. **示例代码：** 提供具体的代码示例
3. **上下文信息：** 包含足够的项目上下文

## 与官网标准的对齐

本配置遵循 Roo Code 官网推荐的最佳实践：

- ✅ 使用目录基础的工作区指令方式
- ✅ 创建模式特定的指令目录
- ✅ 配置文件包含完整的项目信息
- ✅ 指令文件按字母顺序加载
- ✅ 支持多种指令组织方式的兼容性
