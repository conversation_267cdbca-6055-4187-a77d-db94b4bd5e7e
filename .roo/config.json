{"name": "my-react-router-app", "description": "多云CDN管理系统 - AI 编程助手指南", "version": "1.0.0", "ai": {"provider": "roo", "model": "claude-sonnet-4@20250514", "instructions": ".roo/rules/", "copilotInstructions": ".github/copilot-instructions.md", "workspaceInstructions": {"method": "directory-based", "path": ".roo/rules/"}}, "project": {"type": "react-router-v7", "framework": "React Router v7", "mode": "spa", "packageManager": "bun", "styling": "tailwindcss", "componentLibrary": "shadcn/ui"}, "architecture": {"rootComponent": "app/root.tsx", "layoutComponent": "app/components/app-layout.tsx", "routesConfig": "app/routes.ts", "componentsDir": "app/components/", "routesDir": "app/routes/", "typesDir": "app/types/"}, "commands": {"dev": "bun run dev", "build": "bun run build", "start": "bun run start", "typecheck": "bun run typecheck", "lint": "bun run lint", "lint:fix": "bun run lint:fix"}, "conventions": {"language": {"code": "en", "ui": "zh-CN", "comments": "zh-CN", "documentation": "zh-CN"}, "fileNaming": "kebab-case", "componentStructure": "shadcn/ui", "styling": "tailwindcss", "routing": "file-based"}, "modes": {"code": {"instructions": ".roo/rules-code/", "allowedFilePatterns": ["**/*.{ts,tsx,js,jsx,css,json,md}"]}, "architect": {"instructions": ".roo/rules-architect/", "allowedFilePatterns": ["**/*.md", "**/*.json"]}, "debug": {"instructions": ".roo/rules-debug/", "allowedFilePatterns": ["**/*.{ts,tsx,js,jsx,log,md}"]}}, "workspace": {"customInstructions": {"provider": "roo", "language": "zh-CN"}}}