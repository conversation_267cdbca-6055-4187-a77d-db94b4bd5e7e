# 导入和代码修改顺序规则

## 问题描述

在修改代码时，如果先添加导入语句但暂时没有在代码中使用，ESLint 或其他代码检查工具可能会在保存时自动删除这些未使用的导入。

## 解决方案

为了避免这个问题，应该采用以下两种策略之一：

### 策略一：同时进行导入和代码修改

1. 在同一修改操作中，既添加导入语句，又在代码中使用这些导入的组件或函数
2. 这样可以确保导入的组件在添加后立即被使用，避免被自动删除

### 策略二：先修改代码，后添加导入

1. 先修改代码逻辑，使用需要导入的组件或函数（此时会报未定义的错误）
2. 然后添加相应的导入语句
3. 这样可以确保在添加导入时，这些导入已经在代码中被使用

## 最佳实践

- 在进行代码修改时，始终考虑导入语句和代码使用之间的关系
- 使用工具时要注意其自动格式化或清理功能可能对代码修改造成的影响
- 在修改代码前，先规划好需要导入的组件和使用方式，然后一次性完成所有修改
