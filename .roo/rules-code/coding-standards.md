# 代码模式 - 编程标准

## React Router v7 开发规范

### 组件开发

1. **函数组件优先：** 使用函数组件和 React Hooks
2. **TypeScript 严格模式：** 所有组件必须有完整的类型定义
3. **Props 接口：** 为所有组件定义清晰的 Props 接口

```typescript
interface ComponentProps {
  title: string;
  onAction?: () => void;
  children?: React.ReactNode;
}

export function Component({ title, onAction, children }: ComponentProps) {
  // 组件实现
}
```

### 路由开发

1. **文件路由：** 在 `app/routes/` 目录下创建路由文件
2. **路由配置：** 在 `app/routes.ts` 中注册新路由
3. **类型安全：** 使用 TypeScript 确保路由参数类型安全

### 样式规范

1. **Tailwind CSS：** 优先使用 Tailwind CSS 类
2. **响应式设计：** 使用 `sm:`、`md:`、`lg:` 前缀
3. **自定义颜色：** 使用项目特定的颜色变量

```typescript
// 正确的样式使用
<div className="bg-background text-foreground p-4 md:p-6">
  <h1 className="text-2xl font-bold text-lilith-primary">标题</h1>
</div>
```

### 状态管理

1. **本地状态：** 使用 `useState` 管理组件内部状态
2. **表单状态：** 使用受控组件模式
3. **数据获取：** 在组件内部定义 mock 数据

### 错误处理

1. **边界组件：** 实现错误边界组件
2. **类型检查：** 使用 TypeScript 进行编译时检查
3. **运行时验证：** 对外部数据进行验证

## 代码质量要求

### 命名约定

- **文件名：** kebab-case（如 `user-profile.tsx`）
- **组件名：** PascalCase（如 `UserProfile`）
- **变量名：** camelCase（如 `userName`）
- **常量名：** UPPER_SNAKE_CASE（如 `API_BASE_URL`）

### 注释规范

- **组件注释：** 使用 JSDoc 格式
- **复杂逻辑：** 添加行内注释说明
- **TODO 标记：** 使用 `// TODO(ai):` 格式

### 性能优化

1. **React.memo：** 对纯组件使用 memo 优化
2. **useMemo：** 对昂贵计算使用 useMemo
3. **useCallback：** 对回调函数使用 useCallback
