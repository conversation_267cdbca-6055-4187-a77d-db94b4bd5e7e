# 多云CDN管理系统 - AI 编程助手指南

这是一个基于 React Router v7 构建的多云CDN管理系统，专注于云服务商资源的统一管理和流量调度。

## 架构概览

**核心设计理念：** 采用 SPA (Single Page Application) 模式运行，通过 `react-router.config.ts` 中的 `ssr: false` 配置。这是一个前端管理面板，通过模拟数据展示多云CDN管理功能。

**主要组件层次：**

````
│   └── SidebarInset - 主内容区域
```bash
bun install           # 安装依赖（本项目采用 Bun，根目录有 bun.lock）
bun run dev           # 启动开发服务器（端口默认 5173）
bun run typecheck     # 先运行 react-router typegen，再执行 tsc
bun run lint          # ESLint 代码检查
````

└── Route Components (routes/\*.tsx) - 页面级组件

````

- **文件位置：** `app/routes.ts` - 使用 React Router v7 的新配置格式
- **集中式配置：** 采用集中式路由维护（而非文件系统自动路由），统一在 `app/routes.ts` 中更新
### shadcn/ui 集成

- **自定义组件：** 扩展 shadcn/ui 基础组件，如 `cloud-icon.tsx`、`copy-button.tsx`

### shadcn/ui 组件管理规范（重要）

- 组件新增必须通过命令安装，禁止手动拷贝：

    ```bash
    bunx --bun shadcn@latest add <component>
    # 示例：
    bunx --bun shadcn@latest add tabs
    ```

- 不要直接修改 shadcn 生成的文件（通常位于 `app/components/ui/*`）。
    - 需要自定义样式或行为时，创建“扩展/封装”文件，保留原始导出。例如：
        - 在业务目录新增自定义组件：`app/components/ui/status-badge.tsx`（参考当前实现）。
        - 或在同级新增品牌封装：如 `app/components/ui/tabs.brand.tsx`，内部从原组件导入并覆写 `className`/组合样式。
    - 这样升级或重装 shadcn 组件时不会产生冲突。

- 优先通过使用处传入 `className`、`variant`、`size` 等 props 定制；仅当无法满足需求时，才创建封装组件。

- 若确需调整生成文件的样式变量，请优先在 `app/app.css` 中通过 CSS 变量或全局类名完成。

### 数据管理模式

- **模拟数据：** 页面组件内定义 mock 数据（如 `domains.tsx` 中的 `mockDomains`）
- **类型定义：** TypeScript 类型集中在 `app/types/` 目录

````

app/components/
├── ui/ - shadcn/ui 基础组件
├── domains/ - 业务域特定组件（table.tsx：域名列表表格组件）
├── vendors/ - 厂商管理相关组件
├── app-layout.tsx - 应用级布局组件
└── app-sidebar.tsx - 导航侧边栏

````

## 关键开发工作流

### 本地开发

```bash
bun run dev  # 启动开发服务器（端口 5173）
bun run typecheck  # TypeScript 类型检查
bun run lint  # ESLint 代码检查
````

### 构建和部署

```bash
bun run build  # 生产构建到 build/ 目录
bun run start  # 启动生产服务器
docker build -t my-app .  # Docker 容器化部署
```

## 项目特定约定

### 包管理器和工具链

- **Bun 为主：** 本项目使用 Bun 作为主要包管理器和 JavaScript 运行时
- **命令约定：**
    - 使用 `bun add` 代替 `npm install`
    - 使用 `bun run` 代替 `npm run`
    - 使用 `bunx` 代替 `npx` 执行包命令
    - 使用 `bun install` 安装依赖
- **脚本执行：** 所有 package.json 中的脚本都通过 `bun run <script>` 执行

### 样式系统

- **Tailwind CSS 4.x：** 通过 `@tailwindcss/vite` 插件集成
- **自定义颜色：** 使用 `text-lilith-primary`、`text-lilith-muted-foreground` 等项目特定类名
- **响应式：** 采用 mobile-first 设计，侧边栏在小屏幕自动折叠

### 国际化和本地化

- **中文为主：** 界面文本使用中文，注释和代码使用英文
- **时间格式：** 使用相对时间显示（"3 分钟前"、"刚刚"）

### 数据表格模式

- **@tanstack/react-table：** 用于复杂表格功能（排序、筛选、分页）
- **操作模式：** 通过回调函数处理用户操作（`onToggleTrafficScheduling`、`onEdit`、`onDelete`）
- **状态管理：** 表格数据在父组件管理，通过 props 传递操作函数

## 关键集成点

### 云服务商抽象

- **类型定义：** `app/types/cloud.ts` - 定义 CloudProvider 类型
- **图标组件：** `CloudIconWithText` 组件处理不同云服务商的视觉展示
- **厂商特定逻辑：** 在组件层面处理不同云服务商的差异化配置

### 业务逻辑模式

- **域名管理：** 支持多云流量调度开关、HTTPS 配置、状态管理
- **权限模型：** 通过 UI 状态控制功能可用性（如编辑、删除操作）
- **实时更新：** 模拟实时数据更新（`lastUpdateTime` 字段）

## 常见开发任务

### 添加新的云服务商

1. 更新 `app/types/cloud.ts` 中的 `CloudProvider` 类型
2. 在 `CloudIconWithText` 组件添加图标支持
3. 更新相关的模拟数据

### 创建新的管理页面

1. 在 `app/routes/` 创建页面组件
2. 在 `app/routes.ts` 添加路由配置
3. 在 `app-sidebar.tsx` 添加导航项
4. 创建对应的表格组件（如需要）

### 扩展 UI 组件

- 基于 shadcn/ui 组件进行扩展
- 遵循现有的 Tailwind 类名约定
- 使用 `cn()` 工具函数合并样式类

记住：这个项目重点展示 React Router v7 + shadcn/ui 的现代前端开发模式，专注于用户体验和代码质量。

## 常见错误和避免事项

### ESLint 配置错误 (2025年8月5日)

**错误做法：**
在 ESLint 9.x flat config 中错误地认为 `extends` 语法不被支持，导致：

1. 使用 `js.configs.recommended` 和 `...tseslint.configs.recommended.rules` 的冗长写法
2. 误导性地说 flat config 不支持 `extends`

**正确做法：**
ESLint 9.x flat config **完全支持** `extends` 语法，应该使用官方推荐的简洁写法：

```javascript
{
    files: ['**/*.{js,jsx}'],
    plugins: { js },
    extends: ['js/recommended'],  // ✅ 正确：简洁且官方推荐
    rules: { /* 自定义规则 */ }
}
```

**避免错误的关键：**

- 总是参考 ESLint 官方文档的最新示例
- 不要基于过时或不准确的信息做假设
- 当用户提供官方示例时，优先采用官方做法
- flat config 与传统配置在 `extends` 语法上是兼容的

**教训：** 在给出技术建议前，应该先验证当前工具版本的官方文档，而不是基于可能过时的理解。

### 包管理器选择错误 (2025年8月5日)

**错误做法：**
在明确使用 Bun 的项目中，错误地使用 npm/npx 命令：

1. 使用 `npx` 执行包命令，而不是 `bunx`
2. 建议使用 `npm install` 而不是 `bun add`
3. 没有注意到项目根目录存在 `bun.lock` 文件

**正确做法：**
在 Bun 项目中应该统一使用 Bun 生态的命令：

```bash
# ✅ 正确：使用 Bun 命令
bun add <package>           # 安装依赖
bun add -D <package>        # 安装开发依赖
bunx <command>              # 执行包命令
bun run <script>            # 运行脚本

# ❌ 错误：在 Bun 项目中使用 npm
npm install <package>
npx <command>
```

**识别项目包管理器的方法：**

- 检查根目录的锁文件：`bun.lock`、`package-lock.json`、`yarn.lock`
- 查看 package.json 中的 scripts 和依赖版本格式
- 注意项目文档中的安装和运行说明

**教训：** 应该根据项目实际使用的包管理器来提供相应的命令建议，保持工具链的一致性。

## 质量守卫（Quality Gates）

在提交或合并前，建议按以下顺序快速自检：

- 类型检查：确保类型安全且生成最新路由类型
    ```bash
    bun run typecheck
    ```
- 代码规范：修复常见风格与易错项
    ```bash
    bun run lint
    # 可自动修复： bun run lint:fix
    ```
- 构建验证：保证可产出可运行的构建产物
    ```bash
    bun run build
    ```
- 轻量 Smoke Test（可选）：
    ```bash
    bun run start
    # 浏览器访问本地服务，点击路由切换（Home / Domains / Domain Detail / Vendors）
    # 关注控制台报错和 404 资源加载
    ```

备注：当前仓库未引入单元测试框架；若后续添加测试，请在上述流程中加入 `bun run test`。

## 如何新增页面（含简短示例）

1. 在 `app/routes/` 新建页面组件文件（例如 `routes/reports.tsx`）

```tsx
import type { Route } from './+types/reports';

export default function ReportsPage(_props: Route.ComponentProps) {
    return (
        <div className='space-y-4'>
            <h1 className='text-3xl font-bold tracking-tight'>报表中心</h1>
            <p className='text-muted-foreground text-sm'>
                这里展示多云流量/命中率等可视化。
            </p>
        </div>
    );
}
```

2. 在 `app/routes.ts` 中注册集中式路由

```ts
import { route } from '@react-router/dev/routes';
// ...existing routes...
routes.push(route('reports', 'routes/reports.tsx'));
```

3. 在 `app/components/app-sidebar.tsx` 添加导航项（按照现有导航数据结构/组件模式增补一项，指向 `/reports`）。

4. 如需业务组件与类型：

- 组件放在 `app/components/`（可复用 UI 放 `ui/`，业务域放子目录）
- 类型放在 `app/types/`，公共工具放在 `app/lib/`

5. 跑一遍质量守卫：

```bash
bun run typecheck && bun run lint && bun run build
```

若路由路径、别名导入出现问题，检查：

- `vite.config.ts` 中 `@react-router/dev/vite` 与 `vite-tsconfig-paths` 插件是否启用
- 导入是否使用了既有别名（如 `~/components`、`~/lib`、`~/hooks`）

## 常见路由问题排查清单

- 路由注册是否遗漏：`app/routes.ts` 已调用 `route('path', 'routes/file.tsx')`，且文件路径与大小写完全一致。
- Vite 插件是否启用：`vite.config.ts` 包含 `reactRouter()`、`tsconfigPaths()`，缺失会导致路径别名或路由失效。
- 类型是否生成：执行 `bun run typecheck` 触发 `react-router typegen`，确保页面里 `import type { Route } from './+types/xxx'` 可用。
- 运行模式正确：`react-router.config.ts` 中 `ssr: false`，本项目以 SPA 方式运行。
- 动态段是否匹配：集中路由若含 `domains/:domain`，页面里的链接应形如 `/domains/foo`，不要遗漏前缀。
- 开发态专用路由：`.well-known/*` 仅在 `NODE_ENV=development` 生效，生产访问属于预期 404。
- 组件导出规范：路由组件需默认导出 React 组件（`export default function ...`）。
- 构建/缓存问题：若修改路由后不生效，尝试 `bun run clean` 或重启 dev 服务器。

## UI 规范/颜色命名约定

- 优先使用语义化 Token：如 `text-muted-foreground`、`bg-background`、`border`、`ring`（与 shadcn/ui 风格一致）。
- 品牌类名使用：项目中存在 `text-lilith-primary`、`text-lilith-muted-foreground` 等类名，建议仅用于品牌色或特定语义，避免与基础语义冲突。
- 一致的组件尺寸：按钮、输入等优先沿用现有 `size="sm|default"` 与间距规范，避免局部自定义破坏一致性。
- 若发现品牌类名未生效，可按任一方式补齐：
    1. 在 `tailwind.config.ts` 中扩展主题颜色（示例）
        ```ts
        // tailwind.config.ts
        export default {
            content: ['./app/**/*.{js,jsx,ts,tsx}'],
            theme: {
                extend: {
                    colors: {
                        lilith: {
                            primary: '#3B82F6',
                            'muted-foreground': '#6B7280',
                        },
                    },
                },
            },
            plugins: [],
        } as const;
        ```
        随后可用 `text-lilith-primary`、`text-lilith-muted-foreground`。
    2. 或在 `app/app.css` 里定义 CSS 变量并通过工具类引用（适合需要更灵活主题切换的场景）。
