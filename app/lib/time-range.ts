const getTimeRange = (timeRange: string) => {
    const now = new Date();
    const formatTime = (date: Date) => {
        return (
            date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
            }) +
            ' ' +
            date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
            })
        );
    };

    switch (timeRange) {
        case 'today': {
            const startOfDay = new Date(now);
            startOfDay.setHours(0, 0, 0, 0);
            return {
                start: formatTime(startOfDay),
                end: formatTime(now),
            };
        }
        case 'yestoday': {
            const yesterday = new Date(now);
            yesterday.setDate(yesterday.getDate() - 1);
            const startOfYesterday = new Date(yesterday);
            startOfYesterday.setHours(0, 0, 0, 0);
            const endOfYesterday = new Date(yesterday);
            endOfYesterday.setHours(23, 59, 59, 999);
            return {
                start: formatTime(startOfYesterday),
                end: formatTime(endOfYesterday),
            };
        }
        case '7d': {
            const sevenDaysAgo = new Date(now);
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            sevenDaysAgo.setHours(0, 0, 0, 0);
            return {
                start: formatTime(sevenDaysAgo),
                end: formatTime(now),
            };
        }
        default:
            return {
                start: formatTime(
                    new Date(now.getFullYear(), now.getMonth(), now.getDate()),
                ),
                end: formatTime(now),
            };
    }
};

export default getTimeRange;
