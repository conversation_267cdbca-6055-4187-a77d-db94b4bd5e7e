import { useEffect } from 'react';

import {
    isRouteErrorResponse,
    Link,
    Links,
    Meta,
    Outlet,
    Scripts,
    ScrollRestoration,
} from 'react-router';

import { ChevronLeftIcon } from 'lucide-react';

import { Toaster } from '~/components/ui';

import type { Route } from './+types/root';
import './app.css';
import { AppLayout } from './components/app-layout';
import { Button } from './components/ui/base';

export const links: Route.LinksFunction = () => [];

export function Layout({ children }: { children: React.ReactNode }) {
    return (
        <html lang='en' suppressHydrationWarning>
            <head>
                <meta charSet='utf-8' />
                <meta
                    name='viewport'
                    content='width=device-width, initial-scale=1'
                />
                <Meta />
                <Links />
                <link
                    rel='icon'
                    type='image/png'
                    sizes='16x16'
                    href='/lilith-icon.png'
                />
                <link
                    rel='icon'
                    type='image/png'
                    sizes='32x32'
                    href='/lilith-icon.png'
                />
                <link
                    rel='apple-touch-icon'
                    sizes='180x180'
                    href='/lilith-icon.png'
                />
            </head>
            <body>
                {children}
                <Toaster />
                <ScrollRestoration />
                <Scripts />
            </body>
        </html>
    );
}

export function HydrateFallback() {
    return (
        <div className='bg-background flex min-h-screen flex-col items-center justify-center'>
            <div className='animate-in fade-in-0 flex flex-col items-center space-y-4 duration-500'>
                <div className='relative'>
                    <div className='border-primary/30 border-t-primary h-8 w-8 animate-spin rounded-full border-2'></div>
                </div>
                <div className='space-y-1 text-center'>
                    <p className='text-muted-foreground text-sm'>
                        正在初始化应用...
                    </p>
                </div>
            </div>
        </div>
    );
}

export default function App() {
    useEffect(() => {
        // 检查是否是首次显示控制台信息（开发模式下每次都显示）
        const hasShownConsoleMessage = localStorage.getItem(
            'mcdn-console-message-shown',
        );
        const isDev = import.meta.env.DEV;

        if (!hasShownConsoleMessage || isDev) {
            // 设置已显示标记（仅在生产模式）
            if (!isDev) {
                localStorage.setItem('mcdn-console-message-shown', 'true');
            }

            // 显示 ASCII 艺术字和欢迎信息
            // eslint-disable-next-line no-console
            console.log(
                '%c' +
                    '##   ##   ####   #####   ##   ##\n' +
                    '### ###  ##  ##  ##  ##  ###  ##\n' +
                    '## # ##  ##      ##  ##  ## # ##\n' +
                    '##   ##  ##      ##  ##  ##  ###\n' +
                    '##   ##   ####   #####   ##   ##\n' +
                    '================================',
                'color: #D30200; font-weight: bold; font-size: 16px; font-family: "Courier New", Consolas, monospace; line-height: 1.3;',
            );

            // eslint-disable-next-line no-console
            console.log(
                '%c欢迎使用多云CDN管理系统 (MCDN)!\n' +
                    '如需了解更多信息，请访问我们的文档。\n' +
                    '开发团队: Lilith Games',
                'color: #6b7280; font-size: 14px;',
            );

            // 开发模式下添加提示
            if (isDev) {
                // eslint-disable-next-line no-console
                console.log(
                    '%c💡 开发模式：控制台信息每次刷新都会显示',
                    'color: #f59e0b; font-size: 12px;',
                );
            }
        }
    }, []);

    return (
        <AppLayout>
            <Outlet />
        </AppLayout>
    );
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
    // 检测并应用主题
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia(
            '(prefers-color-scheme: dark)',
        ).matches;
        const shouldBeDark =
            savedTheme === 'dark' || (!savedTheme && prefersDark);

        document.documentElement.classList.toggle('dark', shouldBeDark);
    }, []);

    let message = 'Oops!';
    let details = 'An unexpected error occurred.';
    let stack: string | undefined;

    if (isRouteErrorResponse(error)) {
        message = error.status === 404 ? '404' : 'Error';
        details =
            error.status === 404
                ? 'The requested page could not be found.'
                : error.statusText || details;
    } else if (import.meta.env.DEV && error && error instanceof Error) {
        details = error.message;
        stack = error.stack;
    }

    // 404页面专门的设计
    if (isRouteErrorResponse(error) && error.status === 404) {
        return (
            <div className='bg-background flex min-h-screen flex-col items-center justify-center'>
                <div className='mx-auto max-w-md space-y-6 px-4 text-center'>
                    {/* 404数字 */}
                    {/* <div className='text-muted-foreground/40 text-6xl font-bold select-none'> */}
                    <div className='text-lilith-primary text-6xl font-bold select-none'>
                        404
                    </div>

                    {/* 主要信息 */}
                    <div className='space-y-2'>
                        {/* <h1 className='text-foreground text-2xl font-semibold'>
                            页面未找到
                        </h1> */}
                        <p className='text-muted-foreground text-sm leading-relaxed'>
                            抱歉，您访问的页面不存在或已被移动。
                        </p>
                    </div>

                    {/* 操作按钮 */}
                    <div className='flex justify-center gap-3'>
                        <Button variant='link' asChild>
                            <Link to='/'>返回首页</Link>
                        </Button>
                        <Button
                            variant='link'
                            onClick={() => window.history.back()}
                            className='cursor-pointer'
                        >
                            返回上页
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    // 其他错误的通用处理
    return (
        <div className='bg-background flex min-h-screen flex-col items-center justify-center'>
            <div className='mx-auto max-w-lg space-y-6 px-4 text-center'>
                <div className='space-y-2'>
                    <h1 className='text-foreground text-2xl font-semibold'>
                        {message === 'Error' ? '系统错误' : message}
                    </h1>
                    <p className='text-muted-foreground text-sm'>{details}</p>
                </div>

                {/* 开发模式下显示错误堆栈 */}
                {stack && (
                    <details className='bg-muted/50 rounded-lg p-4 text-left text-xs'>
                        <summary className='text-muted-foreground mb-2 cursor-pointer'>
                            查看错误详情
                        </summary>
                        <pre className='text-muted-foreground whitespace-pre-wrap'>
                            <code>{stack}</code>
                        </pre>
                    </details>
                )}

                <Button variant='outline' asChild>
                    <Link to='/'>
                        <ChevronLeftIcon className='mr-2 h-4 w-4' />
                        返回首页
                    </Link>
                </Button>
            </div>
        </div>
    );
}
