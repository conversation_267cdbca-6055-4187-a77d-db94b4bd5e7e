import type { CloudProvider } from './cloud';

/**
 * 域名加速状态
 */
export type DomainStatus = 'active' | 'inactive' | 'configuring' | 'error';

/**
 * 加速类型
 */
export type AccelerationType = 'web' | 'download' | 'video' | 'global';

/**
 * 加速区域
 */
export type AccelerationRegion = 'china' | 'global' | 'overseas';

/**
 * 多云流量调度状态
 */
export type TrafficSchedulingStatus = 'enabled' | 'disabled';

/**
 * HTTPS 状态
 */
export type HttpsStatus = 'enabled' | 'disabled';

/**
 * 域名信息
 */
export interface Domain {
    id: string;
    /** 域名 */
    domain: string;
    /** 多云流量调度状态 */
    trafficScheduling: TrafficSchedulingStatus;
    /** 云服务商 */
    provider: CloudProvider;
    /** 产品类型 */
    product: string;
    /** 加速状态 */
    status: DomainStatus;
    /** 加速类型 */
    accelerationType: AccelerationType;
    /** 加速区域 */
    accelerationRegion: AccelerationRegion;
    /** HTTPS 状态 */
    httpsStatus: HttpsStatus;
    /** 标签 */
    tags: string[];
    /** 项目组 */
    projectGroup: string;
    /** 数据更新时间 */
    lastUpdateTime: string;
}

/**
 * 获取域名加速状态显示文本
 */
export function getDomainAccelerationStatusText(status: DomainStatus): string {
    const statusMap: Record<DomainStatus, string> = {
        active: '已加速',
        inactive: '未加速',
        configuring: '配置中',
        error: '异常',
    };
    return statusMap[status];
}

/**
 * 获取域名状态颜色
 */
export function getDomainStatusColor(status: DomainStatus): string {
    const colorMap: Record<DomainStatus, string> = {
        active: 'text-green-600',
        inactive: 'text-gray-500',
        configuring: 'text-yellow-600',
        error: 'text-red-600',
    };
    return colorMap[status];
}

/**
 * 获取加速类型显示文本
 */
export function getAccelerationTypeText(type: AccelerationType): string {
    const typeMap: Record<AccelerationType, string> = {
        web: '网页加速',
        download: '下载加速',
        video: '视频加速',
        global: '全球加速',
    };
    return typeMap[type];
}

/**
 * 获取加速区域显示文本
 */
export function getAccelerationRegionText(region: AccelerationRegion): string {
    const regionMap: Record<AccelerationRegion, string> = {
        china: '中国内地',
        global: '全球',
        overseas: '全球（不含中国内地）',
    };
    return regionMap[region];
}

/**
 * 获取流量调度状态显示文本
 */
export function getTrafficSchedulingText(
    status: TrafficSchedulingStatus,
): string {
    const statusMap: Record<TrafficSchedulingStatus, string> = {
        enabled: '已启用',
        disabled: '未启用',
    };
    return statusMap[status];
}

/**
 * 获取HTTPS状态显示文本
 */
export function getHttpsStatusText(status: HttpsStatus): string {
    const statusMap: Record<HttpsStatus, string> = {
        enabled: '开启',
        disabled: '关闭',
    };
    return statusMap[status];
}
