/**
 * 云服务提供商类型定义
 */
export const CloudProviders = {
    ALIYUN: 'aliyun',
    TENCENT: 'tencent',
    AWS: 'aws',
    GCP: 'gcp',
    AZURE: 'azure',
    HUAWEI: 'huawei',
    VOLCENGINE: 'volcengine',
    AKAMAI: 'akamai',
} as const;

export type CloudProvider =
    (typeof CloudProviders)[keyof typeof CloudProviders];

/**
 * 云服务提供商信息
 */
export interface CloudProviderInfo {
    name: string;
    icon: string;
    color: string;
    website: string;
    /**
     * 是否使用本地图片
     */
    useLocalImage?: boolean;
}

/**
 * 云服务提供商配置映射
 */
export const CLOUD_PROVIDERS: Record<CloudProvider, CloudProviderInfo> = {
    aliyun: {
        name: '阿里云',
        icon: 'simple-icons:alibabacloud',
        color: '#FF6A00',
        website: 'https://www.aliyun.com',
    },
    tencent: {
        name: '腾讯云',
        icon: 'simple-icons:tencentqq',
        color: '#006EFF',
        website: 'https://cloud.tencent.com',
    },
    aws: {
        name: 'AWS',
        icon: 'simple-icons:amazonaws',
        color: '#FF9900',
        website: 'https://aws.amazon.com',
    },
    gcp: {
        name: 'GCP',
        icon: 'simple-icons:googlecloud',
        color: '#4285F4',
        website: 'https://cloud.google.com',
    },
    azure: {
        name: 'Azure',
        icon: 'simple-icons:microsoftazure',
        color: '#0078D4',
        website: 'https://azure.microsoft.com',
    },
    huawei: {
        name: '华为云',
        icon: 'simple-icons:huawei',
        color: '#FF0000',
        website: 'https://www.huaweicloud.com',
    },
    volcengine: {
        name: '火山引擎',
        icon: '/volcengine.svg', // 使用本地图片
        color: '#1664FF',
        website: 'https://www.volcengine.com',
        useLocalImage: true,
    },
    akamai: {
        name: 'Akamai',
        icon: 'simple-icons:akamai',
        color: '#0096D6',
        website: 'https://www.akamai.com',
    },
};
