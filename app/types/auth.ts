import { Crown, Edit, Eye, Shield } from 'lucide-react';

export type Permission = 'owner' | 'edit' | 'view' | 'deny';

export interface UserPermission {
    userId: string;
    username: string;
    userEmail?: string;
    userAvatar?: string;
    projectId: string;
    projectName: string;
    permission: Permission;
    createdAt: string;
    updatedAt: string;
}

export interface ApiKey {
    id: string;
    name: string;
    key: string;
    userId: string;
    username: string;
    createdAt: string;
    expiresAt: string | null;
    lastUsedAt: string | null;
}

// Mock data for development
export const mockUserPermissions: UserPermission[] = [
    {
        userId: 'user-001',
        username: 'akayj',
        userEmail: '<EMAIL>',
        // userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user-a',
        userAvatar: 'https://github.com/akayj.png',
        projectId: 'proj-0001',
        projectName: '示例项目1',
        permission: 'owner',
        createdAt: '2025-08-13T00:00:00Z',
        updatedAt: '2025-08-13T00:00:00Z',
    },
    {
        userId: 'user-002',
        username: 'akayj',
        userEmail: '<EMAIL>',
        // userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user-b',
        userAvatar: 'https://github.com/akayj.png',
        projectId: 'proj-0001',
        projectName: '示例项目1',
        permission: 'view',
        createdAt: '2025-08-13T00:00:00Z',
        updatedAt: '2025-08-13T00:00:00Z',
    },
    {
        userId: 'user-002',
        username: 'akayj',
        userEmail: '<EMAIL>',
        // userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user-b',
        userAvatar: 'https://github.com/akayj.png',
        projectId: 'proj-0001',
        projectName: '示例项目1',
        permission: 'edit',
        createdAt: '2025-08-13T00:00:00Z',
        updatedAt: '2025-08-13T00:00:00Z',
    },
    {
        userId: 'user-002',
        username: 'akayj',
        userEmail: '<EMAIL>',
        // userAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user-b',
        userAvatar: 'https://github.com/akayj.png',
        projectId: 'proj-0001',
        projectName: '示例项目1',
        permission: 'deny',
        createdAt: '2025-08-13T00:00:00Z',
        updatedAt: '2025-08-13T00:00:00Z',
    },
];

export const mockApiKeys: ApiKey[] = [
    {
        id: 'key-001',
        name: '开发环境',
        key: 'mcdn_dev_xxxxxxxxxxxxx',
        userId: 'user-001',
        username: '甲',
        createdAt: '2025-08-13T00:00:00Z',
        expiresAt: '2026-08-13T00:00:00Z',
        lastUsedAt: '2025-08-13T01:00:00Z',
    },
];

export const getPermissionColor = (permission: Permission): string => {
    const colors = {
        // owner: 'bg-slate-500 hover:bg-slate-600 text-white',
        owner: 'text-lilith-primary',
        edit: 'text-primary',
        view: 'text-primary',
        deny: 'text-muted-foreground',
    };
    return colors[permission];
};

export const getPermissionLabel = (permission: Permission): string => {
    const labels = {
        owner: '所有者',
        edit: '可编辑',
        view: '可查看',
        deny: '拒绝',
    };
    return labels[permission];
};

export const getPermissionIcon = (permission: Permission) => {
    const icons = {
        owner: Crown,
        edit: Edit,
        view: Eye,
        deny: Shield,
    };
    return icons[permission];
};
