import { cn } from '~/lib/utils';

interface SkeletonProps extends React.ComponentProps<'div'> {
    isLoading?: boolean;
    children?: React.ReactNode;
}

function Skeleton({ className, isLoading, children, ...props }: SkeletonProps) {
    if (isLoading === false && children) {
        return <>{children}</>;
    }

    if (isLoading === true || isLoading === undefined) {
        return (
            <div
                data-slot='skeleton'
                className={cn('bg-accent animate-pulse rounded-md', className)}
                {...props}
            />
        );
    }

    return <>{children}</>;
}

// 预定义的骨架屏组件
const SkeletonLine = ({ className, ...props }: React.ComponentProps<'div'>) => (
    <Skeleton className={cn('h-4 w-full rounded', className)} {...props} />
);

const SkeletonCircle = ({
    className,
    ...props
}: React.ComponentProps<'div'>) => (
    <Skeleton className={cn('rounded-full', className)} {...props} />
);

const SkeletonButton = ({
    className,
    ...props
}: React.ComponentProps<'div'>) => (
    <Skeleton className={cn('h-10 rounded-md', className)} {...props} />
);

const SkeletonCard = ({ className, ...props }: React.ComponentProps<'div'>) => (
    <Skeleton className={cn('rounded-xl p-6', className)} {...props} />
);

const SkeletonTable = ({
    rows = 5,
    cols = 6,
    className,
    ...props
}: {
    rows?: number;
    cols?: number;
} & React.ComponentProps<'div'>) => (
    <div className={cn('space-y-2', className)} {...props}>
        {Array.from({ length: rows }).map((_, rowIndex) => (
            <div key={rowIndex} className='flex space-x-2'>
                {Array.from({ length: cols }).map((_, colIndex) => (
                    <Skeleton key={colIndex} className='h-10 flex-1 rounded' />
                ))}
            </div>
        ))}
    </div>
);

export {
    Skeleton,
    SkeletonButton,
    SkeletonCard,
    SkeletonCircle,
    SkeletonLine,
    SkeletonTable,
};
