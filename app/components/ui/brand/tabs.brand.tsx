import * as React from 'react';

import {
    Tabs as BaseTabs,
    <PERSON><PERSON><PERSON>onte<PERSON> as BaseTabsContent,
    <PERSON><PERSON><PERSON><PERSON> as BaseTabsList,
    <PERSON><PERSON><PERSON>rigger as BaseTabsTrigger,
} from '~/components/ui/base';
import { cn } from '~/lib/utils';

// 品牌封装：仅在基础组件上使用主题色作为字体颜色
// 用法：从此文件导入 TabsBrand* 组件替代基础 Tabs*

const TabsBrand = BaseTabs;

const TabsBrandList = React.forwardRef<
    React.ComponentRef<typeof BaseTabsList>,
    React.ComponentPropsWithoutRef<typeof BaseTabsList>
>(({ className, ...props }, ref) => (
    <BaseTabsList ref={ref} className={className} {...props} />
));
TabsBrandList.displayName = 'TabsBrandList';

const TabsBrandTrigger = React.forwardRef<
    React.ComponentRef<typeof BaseTabsTrigger>,
    React.ComponentPropsWithoutRef<typeof BaseTabsTrigger>
>(({ className, ...props }, ref) => (
    <BaseTabsTrigger
        ref={ref}
        className={cn(
            'data-[state=active]:text-lilith-primary font-normal',
            'dark:data-[state=active]:text-lilith-primary',
            // 主题色文字（激活/非激活保持一致）
            'text-lilith-muted-foreground/80 hover:text-lilith-secondary-foreground',
            // 在 dark 模式下添加边框和更明显的激活状态背景
            // 'dark:border dark:border-transparent dark:data-[state=active]:bg-zinc-700/50',
            'hover:text-foreground dark:data-[state=active]:bg-zinc-900',
            className,
        )}
        {...props}
    />
));
TabsBrandTrigger.displayName = 'TabsBrandTrigger';

const TabsBrandContent = React.forwardRef<
    React.ComponentRef<typeof BaseTabsContent>,
    React.ComponentPropsWithoutRef<typeof BaseTabsContent>
>(({ className, ...props }, ref) => (
    <BaseTabsContent ref={ref} className={className} {...props} />
));
TabsBrandContent.displayName = 'TabsBrandContent';

export { TabsBrand, TabsBrandContent, TabsBrandList, TabsBrandTrigger };
