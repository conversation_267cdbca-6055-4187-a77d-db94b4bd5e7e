# ui 目录结构与约定

本目录采用“三层分离”以避免 shadcn 生成件与自定义封装混杂：

- base/：只放 shadcn CLI（bunx --bun shadcn@latest add 组件名）生成的原子组件。请勿直接修改。
- brand/：放品牌封装（对 base 的样式/行为轻量覆写），如 tabs.brand.tsx。
- extras/：放完全自定义的 UI（如 CloudIcon、CopyButton、PageHeader、Chart 等）。

使用规范：

- 业务侧尽量从 `ui/brand` 或 `ui/extras` 导入；避免直接从 `ui/base` 导入（可在 ESLint 中限制）。
- 若必须使用 base 原子件，请通过封装后再引入，便于后续统一样式调整。

shadcn 生成路径：

- components.json 已将 aliases.ui 指向 `~/components/ui/base`，后续安装的组件会自动落入 base/。

迁移建议：

- 过渡期可在 ui 根下放置同名薄壳（re-export 到 base），逐步替换调用方导入，之后删除薄壳。

备注：

- 不要直接修改 base/ 文件；如需更改，请新增 brand/ 封装或在 app/app.css 中以 CSS 变量方式调整样式。
