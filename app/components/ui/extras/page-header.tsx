import * as React from 'react';

import { cn } from '~/lib/utils';

interface PageHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
    title: string;
    description?: string;
}

const PageHeader = React.forwardRef<HTMLDivElement, PageHeaderProps>(
    ({ className, title, description, children, ...props }, ref) => {
        return (
            <div ref={ref} className={cn('mb-6', className)} {...props}>
                <h1 className='text-foreground mb-2 text-3xl font-bold tracking-tight'>
                    {title}
                </h1>
                {description && (
                    <p className='text-muted-foreground'>{description}</p>
                )}
                {children}
            </div>
        );
    },
);
PageHeader.displayName = 'PageHeader';

export { PageHeader };
