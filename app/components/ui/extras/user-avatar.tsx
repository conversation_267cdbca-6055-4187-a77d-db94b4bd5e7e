import {
    Avatar,
    AvatarFallback,
    AvatarImage,
} from '~/components/ui/base/avatar';
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '~/components/ui/base/tooltip';

interface UserAvatarProps {
    username: string;
    userEmail?: string;
    userAvatar?: string;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export function UserAvatar({
    username,
    userEmail,
    userAvatar,
    size = 'md',
    className,
}: UserAvatarProps) {
    const sizeClasses = {
        sm: 'size-6',
        md: 'size-8',
        lg: 'size-10',
    };

    const fallbackText = username.slice(0, 2).toUpperCase();

    const tooltipContent = (
        <div className='text-center'>
            <div className='font-medium'>{username}</div>
            {userEmail && <div className='text-xs opacity-80'>{userEmail}</div>}
        </div>
    );

    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <div className='cursor-pointer'>
                    <Avatar className={`${sizeClasses[size]} ${className}`}>
                        {userAvatar && (
                            <AvatarImage
                                src={userAvatar}
                                alt={`${username} 的头像`}
                            />
                        )}
                        <AvatarFallback className='bg-slate-100 text-xs font-medium text-slate-600'>
                            {fallbackText}
                        </AvatarFallback>
                    </Avatar>
                </div>
            </TooltipTrigger>
            <TooltipContent side='top'>{tooltipContent}</TooltipContent>
        </Tooltip>
    );
}
