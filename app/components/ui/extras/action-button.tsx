import * as React from 'react';

import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '~/lib/utils';

// 创建扩展的按钮变体，包含所有原有变体 + 新的 lilith variant 和 tiny size
const actionButtonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
    {
        variants: {
            variant: {
                default:
                    'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
                destructive:
                    'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
                outline:
                    'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
                secondary:
                    'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
                ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
                link: 'text-primary underline-offset-4 hover:underline',
                lilith: 'bg-lilith-primary text-white shadow-xs hover:bg-lilith-primary/90 focus-visible:ring-lilith-primary/20 dark:focus-visible:ring-lilith-primary/40',
            },
            size: {
                default: 'h-9 px-4 py-2 text-sm has-[>svg]:px-3',
                sm: 'h-8 rounded-md gap-1.5 px-3 text-sm has-[>svg]:px-2.5',
                lg: 'h-10 rounded-md px-6 text-base has-[>svg]:px-4',
                xs: 'h-8 gap-1.5 px-2 text-xs has-[>svg]:px-2',
                tiny: "h-6 rounded gap-1 px-2 text-xs has-[>svg]:px-1.5 [&_svg:not([class*='size-'])]:size-3",
                icon: 'size-9',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    },
);

export interface ActionButtonProps
    extends React.ComponentProps<'button'>,
        VariantProps<typeof actionButtonVariants> {
    asChild?: boolean;
}

const ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : 'button';

        return (
            <Comp
                data-slot='button'
                className={cn(
                    actionButtonVariants({ variant, size, className }),
                    'cursor-pointer',
                )}
                ref={ref}
                {...props}
            />
        );
    },
);

ActionButton.displayName = 'ActionButton';

export { ActionButton, actionButtonVariants };
