import { useState } from 'react';

import { Slot } from '@radix-ui/react-slot';
import { Check, Copy } from 'lucide-react';
import { toast } from 'sonner';

import {
    Button,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '~/components/ui/base';
import { cn } from '~/lib/utils';

interface CopyButtonProps {
    text: string;
    className?: string;
    variant?: 'ghost' | 'outline' | 'default' | 'destructive' | 'secondary';
    size?: 'default' | 'sm' | 'lg' | 'icon';
    tooltipText?: string;
    successDuration?: number;
    iconSize?: string;
    showIcon?: boolean;
    asChild?: boolean;
    children?: React.ReactNode;
}

export function CopyButton({
    text,
    className,
    variant = 'ghost',
    size = 'sm',
    tooltipText = '复制成功',
    successDuration = 1500,
    iconSize = 'h-3 w-3',
    showIcon = true,
    asChild = false,
    children,
}: CopyButtonProps) {
    const [copied, setCopied] = useState(false);

    const copyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), successDuration);
        } catch (error) {
            toast.error('复制失败，请手动复制', {
                description: `${error instanceof Error ? error.message : '未知错误'}`,
                duration: 3000,
                dismissible: true,
                position: 'top-center',
            });
        }
    };

    const renderContent = () => {
        if (asChild && children) {
            return children;
        }

        if (!showIcon) {
            return children || null;
        }

        return copied ? (
            <Check className={cn(iconSize)} />
        ) : (
            <Copy
                className={cn(
                    iconSize,
                    'text-gray-400 transition-colors group-hover:text-gray-700 dark:text-gray-300 dark:group-hover:text-gray-100',
                )}
            />
        );
    };

    const ButtonComponent = asChild ? Slot : Button;

    return (
        <Tooltip open={copied}>
            <TooltipTrigger asChild>
                <ButtonComponent
                    {...(!asChild && {
                        variant,
                        size,
                    })}
                    className={cn(
                        !asChild && 'group cursor-pointer',
                        asChild && 'cursor-pointer',
                        className,
                    )}
                    onClick={copyToClipboard}
                >
                    {renderContent()}
                </ButtonComponent>
            </TooltipTrigger>
            <TooltipContent>
                <p>{tooltipText}</p>
            </TooltipContent>
        </Tooltip>
    );
}
