import * as React from 'react';

import { Icon } from '@iconify/react';

import { Tooltip, TooltipContent, TooltipTrigger } from '~/components/ui/base';
import { cn } from '~/lib/utils';
import { type CloudProvider, CLOUD_PROVIDERS } from '~/types/cloud';

interface CloudIconProps {
    /**
     * 云服务提供商
     */
    provider: CloudProvider;
    /**
     * 图标大小
     */
    size?: number | string;
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 是否显示颜色
     */
    colored?: boolean;
    /**
     * 是否隐藏提示信息
     */
    hiddenTooltip?: boolean;
}

/**
 * 云服务图标组件
 */
const CloudIcon = React.forwardRef<HTMLDivElement, CloudIconProps>(
    (
        {
            provider,
            size = 24,
            className,
            colored = true,
            hiddenTooltip = false,
        },
        _ref,
    ) => {
        const providerInfo = CLOUD_PROVIDERS[provider];

        if (!providerInfo) {
            return null;
        }

        const iconElement = providerInfo.useLocalImage ? (
            <img
                src={providerInfo.icon}
                alt={providerInfo.name}
                width={typeof size === 'number' ? size : 24}
                height={typeof size === 'number' ? size : 24}
                className={cn('inline-block', className)}
                style={{
                    width: size,
                    height: size,
                    objectFit: 'contain',
                }}
            />
        ) : (
            <Icon
                icon={providerInfo.icon}
                width={size}
                height={size}
                className={cn(
                    'inline-block',
                    colored && 'text-current',
                    className,
                )}
                style={colored ? { color: providerInfo.color } : undefined}
            />
        );

        if (!hiddenTooltip) {
            return (
                <Tooltip>
                    <TooltipTrigger asChild>
                        <span className='inline-flex items-center'>
                            {iconElement}
                        </span>
                    </TooltipTrigger>
                    <TooltipContent>{providerInfo.name}</TooltipContent>
                </Tooltip>
            );
        }

        return iconElement;
    },
);
CloudIcon.displayName = 'CloudIcon';

/**
 * 云服务图标与文字组合组件
 */
const CloudIconWithText = React.forwardRef<
    HTMLDivElement,
    CloudIconProps & { showWebsite?: boolean; text?: string }
>(
    (
        {
            provider,
            size = 20,
            className,
            colored = true,
            showWebsite = false,
            text,
            ...props
        },
        ref,
    ) => {
        const providerInfo = CLOUD_PROVIDERS[provider];

        if (!providerInfo) {
            return null;
        }

        // 根据图标大小自适应文字大小
        const getTextSize = (iconSize: number | string) => {
            const numSize =
                typeof iconSize === 'number'
                    ? iconSize
                    : parseInt(iconSize.toString()) || 20;
            if (numSize <= 12) return 'text-xs';
            if (numSize <= 16) return 'text-xs';
            if (numSize <= 20) return 'text-sm';
            if (numSize <= 24) return 'text-sm';
            if (numSize <= 32) return 'text-base';
            return 'text-lg';
        };

        const textSizeClass = getTextSize(size);

        const content = (
            <div
                ref={ref}
                className={cn('inline-flex items-center gap-2', className)}
                {...props}
            >
                <CloudIcon
                    provider={provider}
                    size={size}
                    colored={colored}
                    hiddenTooltip={true}
                />
                <span className={cn(textSizeClass, 'font-normal')}>
                    {text || providerInfo.name}
                </span>
            </div>
        );

        if (showWebsite) {
            return (
                <a
                    href={providerInfo.website}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='transition-opacity hover:opacity-80'
                >
                    {content}
                </a>
            );
        }

        return content;
    },
);
CloudIconWithText.displayName = 'CloudIconWithText';

/**
 * 云服务提供商选择器组件
 */
const CloudProviderGrid = React.forwardRef<
    HTMLDivElement,
    {
        selectedProvider?: CloudProvider;
        onSelect?: (provider: CloudProvider) => void;
        className?: string;
    }
>(({ selectedProvider, onSelect, className, ...props }, ref) => {
    return (
        <div
            ref={ref}
            className={cn(
                'grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5',
                className,
            )}
            {...props}
        >
            {(Object.keys(CLOUD_PROVIDERS) as CloudProvider[]).map(
                (provider) => {
                    const isSelected = selectedProvider === provider;
                    return (
                        <button
                            key={provider}
                            onClick={() => onSelect?.(provider)}
                            className={cn(
                                'flex flex-col items-center gap-2 rounded-lg border p-3 transition-all',
                                'hover:border-blue-300 hover:bg-blue-50',
                                isSelected
                                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                                    : 'border-gray-200 bg-white',
                            )}
                        >
                            <CloudIcon
                                provider={provider}
                                size={32}
                                colored={true}
                                hiddenTooltip={true}
                            />
                            <span className='text-center text-xs font-medium'>
                                {CLOUD_PROVIDERS[provider].name}
                            </span>
                        </button>
                    );
                },
            )}
        </div>
    );
});
CloudProviderGrid.displayName = 'CloudProviderGrid';

export { CloudIcon, CloudIconWithText, CloudProviderGrid };
