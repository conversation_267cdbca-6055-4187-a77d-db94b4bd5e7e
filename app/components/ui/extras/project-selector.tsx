import { useEffect, useRef, useState } from 'react';

import { Check, ChevronDown } from 'lucide-react';

import { Badge } from '~/components/ui/base/badge';
import { cn } from '~/lib/utils';

interface Project {
    id: string;
    name: string;
    description: string;
}

interface ProjectSelectorProps {
    projects: Project[];
    selectedProject: string;
    onProjectChange: (projectId: string) => void;
    className?: string;
}

export function ProjectSelector({
    projects,
    selectedProject,
    onProjectChange,
    className,
}: ProjectSelectorProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const triggerRef = useRef<HTMLButtonElement>(null);

    const currentProject = projects.find((p) => p.id === selectedProject);

    // 点击外部关闭下拉框
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node) &&
                triggerRef.current &&
                !triggerRef.current.contains(event.target as Node)
            ) {
                setIsOpen(false);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleProjectSelect = (projectId: string) => {
        // 如果点击的是当前已选中的项目，不执行任何操作
        if (projectId === selectedProject) {
            return;
        }
        onProjectChange(projectId);
        setIsOpen(false);
    };

    return (
        <div className={cn('relative', className)}>
            <button
                ref={triggerRef}
                onClick={() => setIsOpen(!isOpen)}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                className='focus-visible:ring-ring focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2'
            >
                <Badge
                    variant='outline'
                    className={cn(
                        'cursor-pointer gap-2 px-3 py-1.5 transition-all duration-200',
                        // 默认状态 - 很淡的背景和边框
                        // 'bg-background/40 border-border/50',
                        'bg-muted/40',
                        // hover 状态 - 更明显的背景和边框
                        (isHovered || isOpen) && 'bg-muted border-border/60',
                        // 激活状态
                        isOpen && 'bg-muted/60 border-border/70',
                    )}
                >
                    <span className='text-xs font-medium'>
                        {currentProject?.name}
                    </span>
                    <ChevronDown
                        className={cn(
                            'h-3 w-3 transition-transform duration-200',
                            isOpen && 'rotate-180',
                        )}
                    />
                </Badge>
            </button>

            {isOpen && (
                <div
                    ref={dropdownRef}
                    className='bg-popover border-border absolute top-full left-0 z-50 mt-1 min-w-[200px] rounded-lg border py-2 shadow-lg'
                >
                    {projects.map((project) => (
                        <div key={project.id} className='mx-2'>
                            <button
                                onClick={() => handleProjectSelect(project.id)}
                                className={cn(
                                    'w-full rounded-md px-3 py-2.5 text-left text-xs transition-colors focus:outline-none',
                                    project.id === selectedProject
                                        ? 'cursor-default'
                                        : 'hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer',
                                )}
                            >
                                <div className='flex items-start justify-between gap-2'>
                                    <div className='flex flex-col gap-0.5'>
                                        <span
                                            className={cn(
                                                'font-medium transition-colors',
                                                project.id ===
                                                    selectedProject &&
                                                    'text-lilith-primary',
                                            )}
                                        >
                                            {project.name}
                                        </span>
                                        <span className='text-muted-foreground text-xs'>
                                            {project.description}
                                        </span>
                                    </div>
                                    {project.id === selectedProject && (
                                        <Check className='text-lilith-primary mt-0.5 h-4 w-4 flex-shrink-0' />
                                    )}
                                </div>
                            </button>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
