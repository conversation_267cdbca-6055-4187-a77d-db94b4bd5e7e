import { SidebarInset, SidebarProvider } from '~/components/ui/base';

import { AppSidebar } from './app-sidebar';
import { TopNav } from './top-nav';

interface AppLayoutProps {
    children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
    return (
        <SidebarProvider>
            {/* Header 独占整行，绝对定位在顶部 */}
            <TopNav />

            {/* 原有的侧边栏布局，但添加顶部边距为 header 留空间 */}
            <AppSidebar />
            <SidebarInset className='pt-14'>
                <div className='flex flex-1 flex-col gap-4 p-4'>
                    <div className='min-h-[100vh] flex-1 rounded-xl md:min-h-min'>
                        <main className='px-6 py-0'>{children}</main>
                    </div>
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}
