import { SkeletonTable } from '~/components/ui/base/skeleton';

export function DomainsTableLoading() {
    return (
        <div className='rounded-md border'>
            {/* 表格头部骨架屏 */}
            <div className='bg-lilith-muted/10 dark:bg-lilith-muted/20'>
                <div className='flex'>
                    {Array.from({ length: 11 }).map((_, index) => (
                        <div key={index} className='p-3 text-xs font-medium'>
                            <div className='bg-accent h-4 w-20 animate-pulse rounded' />
                        </div>
                    ))}
                </div>
            </div>

            {/* 表格主体骨架屏 */}
            <SkeletonTable rows={5} cols={11} className='p-4' />
        </div>
    );
}
