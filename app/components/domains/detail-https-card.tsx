import { Link } from 'react-router';

import { CheckCircleIcon } from 'lucide-react';

import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '~/components/ui/base';

interface TlsConfigProp {
    status: string;
}

interface Http2ConfigProp {
    status: string;
}

interface HttpsBasicConfigProp {
    status: string;
    certificateId: string;
    certificateName: string;
    domainName: string;
    expirationTime: string;
}

interface HttpsConfigProp extends HttpsBasicConfigProp {
    tlsConfig: TlsConfigProp;
    http2Config: Http2ConfigProp;
}

function DomainDetailHttpsCard({ data }: { data: HttpsConfigProp }) {
    return (
        <div className='space-y-6'>
            {/* 配置项提示 */}
            <div className='flex items-start gap-2 border-none p-2'>
                <p className='text-muted-foreground text-xs'>
                    配置项的值来自 CDN
                    服务商（简称"CDN"），不同"CDN"支持的具体配置项有差异，对于在"CDN"处不支持的配置项，展示"不支持"；对于在"CDN"处支持但值为空的情况，展示"-"。
                    详情请参考
                    <Button
                        asChild
                        variant='link'
                        size='sm'
                        className='px-0 text-xs'
                    >
                        <Link to='/docs/cdn-config'>文档</Link>
                    </Button>
                </p>
            </div>

            {/* HTTPS配置 */}
            <Card className='w-[60%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle className='font-normal'>HTTPS配置</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-lilith-muted mb-4 text-sm'>
                        HTTPS 配置开启后，客户端可以使用 HTTPS
                        协议访问您的域名。
                    </p>
                    <div className='space-y-4'>
                        <div className='flex items-center justify-between'>
                            <span className='text-sm font-medium'>状态</span>
                            <Badge
                                variant='secondary'
                                className='bg-green-700/10 font-normal text-green-700'
                            >
                                <CheckCircleIcon />
                                {data.status}
                            </Badge>
                        </div>

                        <div className='space-y-4 pt-4'>
                            <h5 className='text-sm font-medium'>证书信息</h5>
                            <Card className='max-w-md rounded-sm py-3 shadow-none'>
                                {/* <CardContent className='p-4'> */}
                                <CardContent>
                                    <div className='space-y-3'>
                                        <div className='flex items-center justify-between'>
                                            <span className='text-lilith-muted-foreground text-xs'>
                                                证书ID
                                            </span>
                                            <span className='text-xs font-normal'>
                                                {data.certificateId}
                                            </span>
                                        </div>
                                        <div className='flex items-center justify-between'>
                                            <span className='text-lilith-muted-foreground text-xs'>
                                                证书名称
                                            </span>
                                            <span className='text-xs font-normal'>
                                                {data.certificateName}
                                            </span>
                                        </div>
                                        <div className='flex items-center justify-between'>
                                            <span className='text-lilith-muted-foreground text-xs'>
                                                适用域名
                                            </span>
                                            <Badge variant='outline' asChild>
                                                <span className='text-lilith-primary font-mono font-normal'>
                                                    {data.domainName}
                                                </span>
                                            </Badge>
                                        </div>
                                        <div className='flex items-center justify-between'>
                                            <span className='text-lilith-muted-foreground text-xs'>
                                                到期时间
                                            </span>
                                            <span className='text-xs font-normal'>
                                                {new Date(
                                                    data.expirationTime,
                                                ).toLocaleString('zh-CN')}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* TLS版本控制 */}
            <Card className='w-[60%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle className='font-normal'>TLS版本控制</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-lilith-muted mb-4 text-sm'>
                        TLS 版本控制开启后，客户端可以使用指定的 SSL/TLS
                        版本访问您的域名。
                    </p>
                    <div className='flex items-center justify-between'>
                        <span className='text-sm font-medium text-gray-700'>
                            版本设置
                        </span>
                        <span className='text-sm text-gray-500'>
                            {data.tlsConfig.status}
                        </span>
                    </div>
                </CardContent>
            </Card>

            {/* HTTP/2配置 */}
            <Card className='w-[60%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle className='font-normal'>HTTP/2配置</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-lilith-muted mb-4 text-sm'>
                        HTTP/2配置开启后，客户端可以使用 HTTP/2 协议访问您的域名
                    </p>
                    <div className='flex items-center justify-between'>
                        <span className='text-sm'>状态</span>
                        <Badge
                            variant='secondary'
                            className='bg-green-700/10 font-normal text-green-700'
                        >
                            <CheckCircleIcon />
                            {data.http2Config.status}
                        </Badge>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

export { DomainDetailHttpsCard, type HttpsConfigProp };
