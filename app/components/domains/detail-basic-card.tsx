import { Link } from 'react-router';

import { ExternalLink } from 'lucide-react';

import { CloudIconWithText, CopyButton } from '~/components/ui';
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '~/components/ui/base';
import type { CloudProvider } from '~/types/cloud';

export interface OriginConfigProp {
    type: string;
    address: string;
    priority: number;
    weight: number;
    backupAddress: string;
    backupHost: string;
    protocol: string;
    httpPort: number;
    httpsPort: number;
    tcpKeepAlive: string;
    httpRequestHeader: string;
}

// 此处定义下 domainData 的类型
export interface DomainDataProp {
    domain: string;
    provider: CloudProvider;
    cname: string;
    createTime: string;
    tags: Record<string, string>;
    accelerationType: string;
    accelerationRegion: string;
    lastModified: string;
    project: string;
    originConfig: OriginConfigProp;
    backToOriginProtocol: {
        allowHttp: string;
        allowHttps: string;
    };
}

function DomainDetailBasicCardBody({ data }: { data: DomainDataProp }) {
    return (
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>域名</span>
                    <div className='flex items-center gap-2'>
                        <CopyButton text={data.domain} asChild showIcon={false}>
                            <Button
                                variant='ghost'
                                size='sm'
                                className='cursor-pointer font-mono text-xs font-normal'
                            >
                                {data.domain}
                            </Button>
                        </CopyButton>
                        {/* <CopyButton text={data.domain} /> */}
                    </div>
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                        云服务商
                    </span>
                    <CloudIconWithText provider={data.provider} />
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>CNAME</span>
                    <div className='flex items-center gap-2'>
                        {/* <CopyButton text={data.cname} asChild showIcon={false}>
                            <span className='hover:bg-accent hover:text-accent-foreground cursor-pointer rounded px-2 py-1 font-mono text-xs transition-colors'>
                                {data.cname}
                            </span>
                        </CopyButton> */}
                        <CopyButton
                            text={data.cname}
                            variant='ghost'
                            size='sm'
                            className='font-mono text-xs font-normal'
                            showIcon={false}
                        >
                            {data.cname}
                        </CopyButton>
                        {/* <CopyButton text={data.cname} /> */}
                    </div>
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                        创建时间
                    </span>
                    <span className='text-sm'>{data.createTime}</span>
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>标签</span>
                    <div className='flex flex-wrap gap-1'>
                        {Object.entries(data.tags).map(([key, value]) => (
                            <Badge
                                key={key}
                                variant='outline'
                                className='text-sm'
                            >
                                {key}:{value}
                            </Badge>
                        ))}
                    </div>
                </div>
            </div>
            <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                        加速类型
                    </span>
                    <span className='text-sm'>{data.accelerationType}</span>
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                        加速区域
                    </span>
                    <span className='text-sm'>{data.accelerationRegion}</span>
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                        最近修改时间
                    </span>
                    <span className='text-sm'>{data.lastModified}</span>
                </div>
                <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                        项目组
                    </span>
                    <Badge asChild variant='secondary'>
                        <Link to='#'>
                            {data.project}
                            <ExternalLink className='ml-1 h-2 w-2' />
                        </Link>
                    </Badge>
                </div>
            </div>
        </div>
    );
}

export function DomainDetailBasicCard({ data }: { data: DomainDataProp }) {
    return (
        <div className='space-y-6'>
            {/* 配置项提示 */}
            <div className='flex items-start gap-2 rounded-sm border-none p-2'>
                {/* <p className='text-lilith-muted-foreground/50 dark:text-muted-foreground text-xs'> */}
                <p className='text-muted-foreground text-xs'>
                    配置项的值来自 CDN
                    服务商（简称"CDN"），不同"CDN"支持的具体配置项有差异，对于在"CDN"处不支持的配置项，展示"不支持"；对于在"CDN"处支持但值信息为空的情况，展示"-"。
                    详情请参考
                    <Button
                        asChild
                        variant='link'
                        size='sm'
                        className='px-0 text-xs'
                    >
                        <Link to='/docs/cdn-config'>文档</Link>
                    </Button>
                </p>
            </div>

            {/* 基本信息 */}
            <Card className='max-w-[90%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                    <DomainDetailBasicCardBody data={data} />
                </CardContent>
            </Card>

            {/* 回源站配置 */}
            <Card className='border-none shadow-none'>
                <CardHeader>
                    <CardTitle>回源站配置</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-muted-foreground mb-4 text-sm'>
                        CDN
                        回源时对源站的域名设置及验证级别的源站配置，源站级别配置会对源站下下方的各源站地址生效。
                    </p>
                    {/* Use table-fixed and allow wrapping on long cells to avoid horizontal scroll */}
                    <Table className='border'>
                        <TableHeader>
                            <TableRow className='bg-muted text-sm'>
                                <TableHead>类型</TableHead>
                                <TableHead>回源地址</TableHead>
                                <TableHead>优先级</TableHead>
                                <TableHead>权重</TableHead>
                                <TableHead>回源Host类型</TableHead>
                                <TableHead>回源Host</TableHead>
                                <TableHead>回源协议</TableHead>
                                <TableHead>Http端口</TableHead>
                                <TableHead>Https端口</TableHead>
                                <TableHead>TCP长连接</TableHead>
                                <TableHead>Http请求超时</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <TableRow>
                                <TableCell className='text-xs'>
                                    {data.originConfig.type}
                                </TableCell>
                                {/* Long origin address: allow wrap and break long tokens */}
                                <TableCell className='font-mono text-xs break-all !whitespace-normal'>
                                    {data.originConfig.address}
                                </TableCell>
                                <TableCell className='text-xs'>
                                    {data.originConfig.priority}
                                </TableCell>
                                <TableCell className='text-xs'>
                                    {data.originConfig.weight}
                                </TableCell>
                                <TableCell className='text-xs break-all !whitespace-normal'>
                                    {data.originConfig.backupAddress}
                                </TableCell>
                                <TableCell className='text-xs break-all !whitespace-normal'>
                                    {data.originConfig.backupHost}
                                </TableCell>
                                <TableCell className='text-xs'>
                                    {data.originConfig.protocol}
                                </TableCell>
                                <TableCell className='text-xs'>
                                    {data.originConfig.httpPort}
                                </TableCell>
                                <TableCell className='text-xs'>
                                    {data.originConfig.httpsPort}
                                </TableCell>
                                <TableCell className='text-xs'>
                                    {data.originConfig.tcpKeepAlive}
                                </TableCell>
                                <TableCell className='text-xs break-all !whitespace-normal'>
                                    {data.originConfig.httpRequestHeader}
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            {/* 回源协议与端口 */}
            <Card className='w-[60%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle>回源协议与端口</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='mb-4 text-sm text-gray-200'>
                        CDN
                        回源时使用的协议及端口，全局回源协议会对源站下方的各源站地址生效。
                    </p>
                    <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                        <div className='space-y-3'>
                            <div className='flex items-center justify-between'>
                                <span className='text-sm text-gray-400'>
                                    全局回源协议
                                </span>
                                <span className='text-sm'>
                                    {data.backToOriginProtocol.allowHttp}
                                </span>
                            </div>
                        </div>
                        <div className='space-y-3'>
                            <div className='flex items-center justify-between'>
                                <span className='text-sm text-gray-400'>
                                    全局Https回源
                                </span>
                                <span className='text-sm'>
                                    {data.backToOriginProtocol.allowHttps}
                                </span>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
