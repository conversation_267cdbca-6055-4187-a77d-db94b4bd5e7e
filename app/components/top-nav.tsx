import { useEffect, useRef, useState } from 'react';

import { Link } from 'react-router';

import { Key, LogOut, Moon, Settings, Sun, Users } from 'lucide-react';

import {
    Avatar,
    AvatarFallback,
    AvatarImage,
    Button,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    SidebarTrigger,
    Skeleton,
} from '~/components/ui/base';
import { ProjectSelector } from '~/components/ui/extras/project-selector';

// Mock project data
const mockProjects = [
    { id: 'lilith-games', name: 'Lilith Games', description: '主要游戏项目' },
    { id: 'farlight-84', name: 'Farlight 84', description: '射击游戏项目' },
    {
        id: 'rise-of-kingdoms',
        name: 'Rise of Kingdoms',
        description: '策略游戏项目',
    },
];

export function TopNav() {
    const [isDark, setIsDark] = useState(false);
    const [selectedProject, setSelectedProject] = useState('lilith-games');
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // 清理计时器
    const clearHoverTimeout = () => {
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
            hoverTimeoutRef.current = null;
        }
    };

    // 处理鼠标进入
    const handleMouseEnter = () => {
        clearHoverTimeout();
        setIsUserMenuOpen(true);
    };

    // 处理鼠标离开（带延迟）
    const handleMouseLeave = () => {
        clearHoverTimeout();
        hoverTimeoutRef.current = setTimeout(() => {
            setIsUserMenuOpen(false);
        }, 150); // 150ms 延迟，给用户时间移动到下拉菜单
    };

    // 组件卸载时清理计时器
    useEffect(() => {
        return () => clearHoverTimeout();
    }, []);

    // 初始化主题状态
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia(
            '(prefers-color-scheme: dark)',
        ).matches;
        const shouldBeDark =
            savedTheme === 'dark' || (!savedTheme && prefersDark);

        setIsDark(shouldBeDark);
        document.documentElement.classList.toggle('dark', shouldBeDark);
    }, []);

    // 切换主题
    const toggleTheme = () => {
        const newIsDark = !isDark;
        setIsDark(newIsDark);
        document.documentElement.classList.toggle('dark', newIsDark);
        localStorage.setItem('theme', newIsDark ? 'dark' : 'light');
    };

    return (
        <header className='bg-background/95 supports-[backdrop-filter]:bg-background/60 border-border/40 fixed top-0 right-0 left-0 z-50 border-b backdrop-blur'>
            <div className='flex h-14 items-center px-4'>
                {/* 左侧：Logo 和 Project 选择器 */}
                <div className='flex items-center gap-4'>
                    <Link to='/' className='flex items-center gap-2'>
                        <img
                            src='/lilith-icon.png'
                            alt='Lilith'
                            className='h-6 w-6'
                        />
                        <span className='text-lg font-bold'>MCDN</span>
                    </Link>

                    {/* Project 下拉选择器 */}
                    <ProjectSelector
                        projects={mockProjects}
                        selectedProject={selectedProject}
                        onProjectChange={setSelectedProject}
                    />
                </div>

                {/* 中间：占位符，可以放搜索或其他功能 */}
                <div className='flex-1' />

                {/* 右侧：操作按钮 */}
                <div className='flex items-center gap-2'>
                    {/* 主题切换 */}
                    <Button
                        variant='ghost'
                        size='icon'
                        className='size-7'
                        onClick={toggleTheme}
                    >
                        <Sun className='h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90' />
                        <Moon className='absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0' />
                        <span className='sr-only'>切换主题</span>
                    </Button>

                    {/* 用户头像和下拉菜单 */}
                    <div
                        className='relative'
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                    >
                        <DropdownMenu
                            open={isUserMenuOpen}
                            onOpenChange={setIsUserMenuOpen}
                        >
                            <DropdownMenuTrigger asChild>
                                <div className='group'>
                                    <Avatar className='border-background h-6 w-6 opacity-70 transition-all duration-200 group-hover:opacity-100'>
                                        <AvatarImage
                                            src='https://github.com/akayj.png'
                                            alt='@akayj'
                                        />
                                        <AvatarFallback>
                                            <Skeleton className='h-6 w-6 rounded-full' />
                                        </AvatarFallback>
                                    </Avatar>
                                </div>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end' className='w-56'>
                                <DropdownMenuLabel className='font-normal'>
                                    <div className='flex flex-col space-y-1'>
                                        <p className='text-sm leading-none font-medium'>
                                            akayj
                                        </p>
                                        <p className='text-muted-foreground text-xs leading-none'>
                                            <EMAIL>
                                        </p>
                                    </div>
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem asChild>
                                    <Link
                                        to='/auth/users'
                                        className='flex items-center gap-2'
                                    >
                                        <Users className='h-4 w-4' />
                                        <span>用户授权</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem asChild>
                                    <Link
                                        to='/auth/api-keys'
                                        className='flex items-center gap-2'
                                    >
                                        <Key className='h-4 w-4' />
                                        <span>API Key</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem asChild>
                                    <Link
                                        to='/settings'
                                        className='flex items-center gap-2'
                                    >
                                        <Settings className='h-4 w-4' />
                                        <span>系统设置</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className='text-red-600 focus:text-red-600'>
                                    <LogOut className='mr-2 h-4 w-4' />
                                    <span>退出登录</span>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>

                    {/* 侧边栏切换按钮 */}
                    <SidebarTrigger className='text-muted-foreground hover:text-foreground transition-colors' />
                </div>
            </div>
        </header>
    );
}
