import * as React from 'react';

import {
    Activity,
    BarChart3,
    CalendarDays,
    FileText,
    RefreshCw,
} from 'lucide-react';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from 'recharts';

import {
    ChartContainer,
    ChartLegend,
    ChartLegendContent,
    ChartTooltip,
    ChartTooltipContent,
    type ChartConfig,
} from '~/components/ui';
import {
    Button,
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '~/components/ui/base';
import getTimeRange from '~/lib/time-range';
import { cn } from '~/lib/utils';

export function meta() {
    return [
        { title: '内容加速概览 - Lilith MCDN' },
        { name: 'description', content: '多云CDN内容加速管理平台概览页面' },
    ];
}

// 模拟CDN流量数据 - 增加更多起伏
const chartData = [
    { time: '08-05 00:00', traffic: 0.02, bandwidth: 0.01, requests: 120 },
    { time: '08-05 01:00', traffic: 0.01, bandwidth: 0.008, requests: 80 },
    { time: '08-05 02:00', traffic: 0.008, bandwidth: 0.005, requests: 60 },
    { time: '08-05 03:00', traffic: 0.015, bandwidth: 0.012, requests: 110 },
    { time: '08-05 04:00', traffic: 0.025, bandwidth: 0.018, requests: 180 },
    { time: '08-05 05:00', traffic: 0.018, bandwidth: 0.014, requests: 140 },
    { time: '08-05 06:00', traffic: 0.035, bandwidth: 0.025, requests: 250 },
    { time: '08-05 07:00', traffic: 0.065, bandwidth: 0.048, requests: 420 },
    { time: '08-05 08:00', traffic: 0.085, bandwidth: 0.065, requests: 580 },
    { time: '08-05 09:00', traffic: 0.12, bandwidth: 0.095, requests: 780 },
    { time: '08-05 10:00', traffic: 0.095, bandwidth: 0.075, requests: 650 },
    { time: '08-05 11:00', traffic: 0.15, bandwidth: 0.118, requests: 920 },
    { time: '08-05 12:00', traffic: 0.22, bandwidth: 0.175, requests: 1350 },
    { time: '08-05 13:00', traffic: 0.28, bandwidth: 0.22, requests: 1680 },
    { time: '08-05 14:00', traffic: 0.25, bandwidth: 0.195, requests: 1450 },
    { time: '08-05 15:00', traffic: 0.195, bandwidth: 0.155, requests: 1180 },
    { time: '08-05 16:00', traffic: 0.235, bandwidth: 0.185, requests: 1420 },
    { time: '08-05 17:00', traffic: 0.18, bandwidth: 0.145, requests: 1080 },
    { time: '08-05 18:00', traffic: 0.165, bandwidth: 0.132, requests: 980 },
    { time: '08-05 19:00', traffic: 0.205, bandwidth: 0.165, requests: 1250 },
    { time: '08-05 20:00', traffic: 0.32, bandwidth: 0.255, requests: 1920 },
    { time: '08-05 21:00', traffic: 0.385, bandwidth: 0.31, requests: 2350 },
    { time: '08-05 22:00', traffic: 0.295, bandwidth: 0.235, requests: 1780 },
    { time: '08-05 23:00', traffic: 0.155, bandwidth: 0.125, requests: 920 },
];

const chartConfig = {
    traffic: {
        label: '流量',
        color: 'hsl(var(--lilith-primary))',
    },
    bandwidth: {
        label: '带宽',
        color: 'hsl(var(--chart-1))',
    },
    requests: {
        label: '请求数',
        color: 'hsl(var(--chart-2))',
    },
} satisfies ChartConfig;

export default function Home() {
    const [timeRange, setTimeRange] = React.useState('today');
    const [selectedTab, setSelectedTab] = React.useState('流量');

    // 计算时间范围，避免重复调用
    const timeRangeData = React.useMemo(
        () => getTimeRange(timeRange),
        [timeRange],
    );

    return (
        <div className='space-y-6'>
            {/* 页面标题和控制区域 */}
            <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
                <div>
                    <h1 className='text-2xl font-bold'>内容加速概览</h1>
                    <p className='text-muted-foreground px-1 text-sm'>
                        仅统计域名在多云CDN的置加速上的用量。
                    </p>
                </div>
                <div className='flex items-center gap-2'>
                    <Select value={timeRange} onValueChange={setTimeRange}>
                        <SelectTrigger
                            className='hidden w-[150px] rounded-lg sm:ml-auto sm:flex'
                            aria-label='Select a value'
                        >
                            <SelectValue placeholder='今天' />
                        </SelectTrigger>
                        <SelectContent className='rounded-xl'>
                            <SelectItem value='today' className='rounded-lg'>
                                今天
                            </SelectItem>
                            <SelectItem value='yestoday' className='rounded-lg'>
                                昨天
                            </SelectItem>
                            <SelectItem value='7d' className='rounded-lg'>
                                最近七天
                            </SelectItem>
                        </SelectContent>
                    </Select>
                    <div className='flex items-center gap-2 rounded-lg border px-3 py-2'>
                        <CalendarDays className='h-4 w-4' />
                        <span className='text-sm'>{timeRangeData.start}</span>
                        <span className='text-muted-foreground'>-</span>
                        <span className='text-sm'>{timeRangeData.end}</span>
                        <CalendarDays className='h-4 w-4' />
                    </div>
                    <Button variant='outline' size='sm'>
                        <RefreshCw className='h-4 w-4' />
                    </Button>
                </div>
            </div>

            {/* 统计卡片 */}
            <div className='grid gap-4 md:grid-cols-4'>
                <Card>
                    <CardHeader className='pb-2'>
                        <CardDescription>加速域名</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className='flex items-baseline gap-1'>
                            <span className='text-2xl font-bold'>10</span>
                            <span className='text-muted-foreground text-sm'>
                                个
                            </span>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className='pb-2'>
                        <CardDescription>流量</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className='flex items-baseline gap-1'>
                            <span className='text-2xl font-bold'>100</span>
                            <span className='text-muted-foreground text-sm'>
                                PB
                            </span>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className='pb-2'>
                        <CardDescription>带宽峰值</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className='flex items-baseline gap-1'>
                            <span className='text-2xl font-bold'>234</span>
                            <span className='text-muted-foreground text-sm'>
                                Gbps
                            </span>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className='pb-2'>
                        <CardDescription>总请求数</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className='flex items-baseline gap-1'>
                            <span className='text-2xl font-bold'>9999</span>
                            <span className='text-muted-foreground text-sm'>
                                次
                            </span>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* 图表区域 */}
            <Card className='col-span-4'>
                <CardHeader className='flex flex-row items-center justify-between'>
                    <div>
                        <h3 className='font-bold'>趋势数据</h3>
                        {/* <p className='text-muted-foreground text-sm'>
                            流量、带宽、请求数（过去 30 天）
                        </p> */}
                    </div>
                    <div className='flex'>
                        {[
                            { icon: Activity, name: '流量' },
                            { icon: BarChart3, name: '带宽' },
                            { icon: FileText, name: '请求数' },
                        ].map((tab) => {
                            const IconComponent = tab.icon;
                            return (
                                <Button
                                    key={tab.name}
                                    variant={
                                        selectedTab === tab.name
                                            ? 'link'
                                            : 'ghost'
                                    }
                                    size='sm'
                                    onClick={() => setSelectedTab(tab.name)}
                                    className={cn(
                                        'cursor-pointer',
                                        selectedTab === tab.name
                                            ? 'text-lilith-primary hover:no-underline'
                                            : '',
                                    )}
                                >
                                    <div className='flex items-center gap-1'>
                                        <IconComponent className='h-4 w-4' />
                                        <span className='text-xs'>
                                            {tab.name}
                                        </span>
                                    </div>
                                </Button>
                            );
                        })}
                    </div>
                </CardHeader>
                <CardContent className='pl-2'>
                    <ChartContainer
                        config={chartConfig}
                        className='aspect-auto h-[400px] w-full'
                    >
                        <AreaChart
                            accessibilityLayer
                            data={chartData}
                            margin={{
                                left: 12,
                                right: 12,
                                top: 20,
                                bottom: 12,
                            }}
                        >
                            <defs>
                                <linearGradient
                                    id='fillTraffic'
                                    x1='0'
                                    y1='0'
                                    x2='0'
                                    y2='1'
                                >
                                    <stop
                                        offset='5%'
                                        stopColor='var(--color-chart-1)'
                                        stopOpacity={0.8}
                                    />
                                    <stop
                                        offset='95%'
                                        stopColor='var(--color-chart-1)'
                                        stopOpacity={0.1}
                                    />
                                </linearGradient>
                                <linearGradient
                                    id='fillBandwidth'
                                    x1='0'
                                    y1='0'
                                    x2='0'
                                    y2='1'
                                >
                                    <stop
                                        offset='5%'
                                        stopColor='var(--color-chart-1)'
                                        stopOpacity={0.8}
                                    />
                                    <stop
                                        offset='95%'
                                        stopColor='var(--color-chart-1)'
                                        stopOpacity={0.1}
                                    />
                                </linearGradient>
                                <linearGradient
                                    id='fillRequests'
                                    x1='0'
                                    y1='0'
                                    x2='0'
                                    y2='1'
                                >
                                    <stop
                                        offset='5%'
                                        stopColor='var(--color-chart-1)'
                                        stopOpacity={0.8}
                                    />
                                    <stop
                                        offset='95%'
                                        stopColor='var(--color-chart-1)'
                                        stopOpacity={0.1}
                                    />
                                </linearGradient>
                            </defs>
                            <CartesianGrid vertical={false} />
                            <XAxis
                                dataKey='time'
                                tickLine={false}
                                axisLine={false}
                                tickMargin={8}
                                tickFormatter={(value) => {
                                    // 只显示时间部分
                                    return value.split(' ')[1];
                                }}
                            />
                            <YAxis
                                tickLine={false}
                                axisLine={false}
                                tickMargin={8}
                                tickFormatter={(value) => {
                                    if (selectedTab === '请求数') {
                                        return `${value}`;
                                    }
                                    return `${value}B`;
                                }}
                            />
                            <ChartTooltip
                                cursor={false}
                                content={
                                    <ChartTooltipContent
                                        labelFormatter={(value) => {
                                            return `时间: ${value}`;
                                        }}
                                        indicator='dot'
                                    />
                                }
                            />
                            {selectedTab === '全部' ? (
                                <>
                                    <Area
                                        dataKey='bandwidth'
                                        type='monotone'
                                        fill='url(#fillBandwidth)'
                                        stroke='var(--color-chart-1)'
                                        strokeWidth={2}
                                        stackId='a'
                                    />
                                    <Area
                                        dataKey='traffic'
                                        type='monotone'
                                        fill='url(#fillTraffic)'
                                        stroke='var(--color-chart-1)'
                                        strokeWidth={2}
                                        stackId='a'
                                    />
                                    <ChartLegend
                                        content={<ChartLegendContent />}
                                    />
                                </>
                            ) : selectedTab === '流量' ? (
                                <Area
                                    dataKey='traffic'
                                    type='monotone'
                                    fill='url(#fillTraffic)'
                                    stroke='var(--color-chart-1)'
                                    strokeWidth={2}
                                />
                            ) : selectedTab === '带宽' ? (
                                <Area
                                    dataKey='bandwidth'
                                    type='monotone'
                                    fill='url(#fillBandwidth)'
                                    stroke='var(--color-chart-1)'
                                    strokeWidth={2}
                                />
                            ) : (
                                <Area
                                    dataKey='requests'
                                    type='monotone'
                                    fill='url(#fillRequests)'
                                    stroke='var(--color-chart-1)'
                                    strokeWidth={2}
                                />
                            )}
                        </AreaChart>
                    </ChartContainer>
                </CardContent>
            </Card>
        </div>
    );
}
