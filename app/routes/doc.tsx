import { useEffect, useState } from 'react';

import { Link } from 'react-router';

import { FileText, Home } from 'lucide-react';

import {
    Button,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '~/components/ui/base';

export default function Doc() {
    const [content, setContent] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // 加载 markdown 文件内容
        const loadContent = async () => {
            try {
                setLoading(true);
                // 从 public/doc/poc.md 加载内容
                const response = await fetch('/doc/poc.md');
                if (!response.ok) {
                    throw new Error(
                        `Failed to load document: ${response.status} ${response.statusText}`,
                    );
                }
                const text = await response.text();
                setContent(text);
            } catch (err) {
                setError(
                    err instanceof Error
                        ? err.message
                        : 'Failed to load document',
                );
                setContent('# 文档加载失败\n\n请稍后重试或联系管理员。');
            } finally {
                setLoading(false);
            }
        };

        loadContent();
    }, []);

    // 简单的 markdown 解析函数
    const renderMarkdown = (markdown: string) => {
        return markdown.split('\n\n').map((paragraph, index) => {
            // 标题
            if (paragraph.startsWith('# ')) {
                return (
                    <h1 key={index} className='text-2xl font-bold'>
                        {paragraph.substring(2)}
                    </h1>
                );
            } else if (paragraph.startsWith('## ')) {
                return (
                    <h2 key={index} className='mt-6 mb-3 text-xl font-semibold'>
                        {paragraph.substring(3)}
                    </h2>
                );
            } else if (paragraph.startsWith('### ')) {
                return (
                    <h3 key={index} className='mt-4 mb-2 text-lg font-medium'>
                        {paragraph.substring(4)}
                    </h3>
                );
            }
            // 列表
            else if (paragraph.startsWith('- ')) {
                return (
                    <ul key={index} className='list-disc space-y-1 pl-5'>
                        {paragraph.split('\n').map((item, i) => (
                            <li key={i}>{item.substring(2)}</li>
                        ))}
                    </ul>
                );
            }
            // 普通段落
            else {
                return (
                    <p key={index} className='mb-4'>
                        {paragraph}
                    </p>
                );
            }
        });
    };

    return (
        <div className='space-y-6'>
            <div className='space-y-2'>
                <h1 className='text-3xl font-bold tracking-tight'>
                    产品设计文档
                </h1>
                <p className='text-muted-foreground'>
                    多云CDN管理系统 (MCDN) 的详细设计说明
                </p>
            </div>

            <div className='flex gap-4'>
                <Button asChild>
                    <Link to='/'>
                        <Home className='mr-2 h-4 w-4' />
                        返回首页
                    </Link>
                </Button>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                        <FileText className='h-5 w-5' />
                        <span>设计文档 (POC)</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className='flex items-center justify-center py-12'>
                            <div className='text-muted-foreground'>
                                加载中...
                            </div>
                        </div>
                    ) : error ? (
                        <div className='text-destructive py-4'>{error}</div>
                    ) : (
                        <div className='prose prose-gray dark:prose-invert max-w-none'>
                            {renderMarkdown(content)}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
