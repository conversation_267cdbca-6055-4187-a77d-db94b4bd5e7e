import { useState } from 'react';

import { Link } from 'react-router';

import {
    ArrowLeft,
    CheckCircleIcon,
    Code2Icon,
    EditIcon,
    FileText,
    RefreshCcwIcon,
} from 'lucide-react';

import { DomainDetailBasicCard } from '~/components/domains/detail-basic-card';
import { DomainDetailHttpsCard } from '~/components/domains/detail-https-card';
import { ActionButton, CloudIconWithText } from '~/components/ui';
import {
    Badge,
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
    Button,
} from '~/components/ui/base';
import {
    TabsBrand as Tabs,
    TabsBrandContent as TabsContent,
    <PERSON>bsBrand<PERSON>ist as TabsList,
    TabsBrandTrigger as TabsTrigger,
} from '~/components/ui/brand/tabs.brand';

import type { Route } from './+types/domains.$domain';

function BreadcrumbWithCustomSeparator() {
    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                        <Link to='/domains'>域名列表</Link>
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbPage>详情</BreadcrumbPage>
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    );
}

// 模拟域名详情数据
function getMockDomainData(domain: string) {
    return {
        basic: {
            domain,
            provider: 'aliyun' as const,
            status: '已启用',
            accelerationType: '网页加速',
            accelerationRegion: '中国内地',
            cname: `${domain}.w.kunluncan.com`,
            createTime: '2025-07-28 17:29:20',
            lastModified: '2025-07-29 11:15:58',
            project: 'default',
            tags: { env: 'test' },
            originConfig: {
                type: '阿里云OSS',
                address: 'tsh-ad-officialweb-prod-oss-cn-shanghai.aliyuncs.com',
                priority: 20,
                weight: 10,
                backupAddress: '-',
                backupHost: '-',
                protocol: '合并HTTP',
                httpPort: 80,
                httpsPort: 443,
                tcpKeepAlive: '不支持',
                httpRequestHeader: '不支持',
            },
            backToOriginProtocol: {
                allowHttp: '-',
                allowHttps: '-',
            },
        },
        httpsConfig: {
            status: '已开启',
            certificateId: '18629415',
            certificateName: 'cert-13946753',
            domainName: '*.farlightgame.com',
            expirationTime: '2026-05-22T07:59:59+08:00',
            tlsConfig: {
                status: '--',
            },
            http2Config: {
                status: '已开启',
            },
        },
    };
}

// 使用 shadcn Tabs 组件构建轻量级标签导航

export default function DomainDetail({ params }: Route.ComponentProps) {
    const [activeTab, setActiveTab] = useState('basic');
    const domainData = getMockDomainData(params.domain);

    const tabs = [
        { id: 'basic', label: '基础配置' },
        { id: 'https', label: 'HTTPS配置' },
        { id: 'access', label: '访问控制' },
        { id: 'cache', label: '缓存配置' },
        { id: 'sync', label: '同步配置' },
        { id: 'content', label: '内容优化' },
    ];

    return (
        <div className='space-y-6'>
            {/* 面包屑导航 */}
            <BreadcrumbWithCustomSeparator />

            {/* 顶部导航 */}
            <div className='flex items-center justify-between'>
                <div className='flex items-center gap-4'>
                    <Link to='/domains'>
                        <Button variant='ghost' size='sm'>
                            <ArrowLeft className='h-4 w-4' />
                        </Button>
                    </Link>
                    <div className='flex items-center gap-2'>
                        <span className='text-lg font-medium'>
                            {params.domain}
                        </span>
                        <Badge variant='outline'>
                            <CloudIconWithText
                                provider={domainData.basic.provider}
                                size={12}
                                className='text-normal'
                            />
                        </Badge>
                        <Badge
                            variant='outline'
                            className='font-normal text-green-700'
                        >
                            <CheckCircleIcon className='mr-1 h-4 w-4' />
                            已启用
                        </Badge>
                    </div>
                </div>
                <div className='flex items-center gap-2'>
                    <ActionButton variant='outline' size='xs'>
                        <FileText className='mr-1 h-2 w-2' />
                        操作日志
                    </ActionButton>
                    <ActionButton variant='outline' size='xs'>
                        <RefreshCcwIcon className='mr-1 h-2 w-2' />
                        预热
                    </ActionButton>
                    <ActionButton variant='outline' size='xs'>
                        刷新
                    </ActionButton>
                    <ActionButton size='xs' variant='lilith'>
                        <EditIcon className='mr-1 h-2 w-2' />
                        编辑
                    </ActionButton>
                </div>
            </div>

            {/* 标签页导航 + 内容（shadcn Tabs） */}
            <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v)}>
                <TabsList>
                    {tabs.map((tab) => (
                        <TabsTrigger
                            key={tab.id}
                            value={tab.id}
                            className='text-sm data-[state=inactive]:cursor-pointer'
                        >
                            {tab.label}
                        </TabsTrigger>
                    ))}
                </TabsList>

                <TabsContent value='basic' className='mt-2 min-h-[400px]'>
                    <DomainDetailBasicCard data={domainData.basic} />
                </TabsContent>
                <TabsContent value='https' className='mt-2 min-h-[400px]'>
                    <DomainDetailHttpsCard data={domainData.httpsConfig} />
                </TabsContent>
                <TabsContent value='access' className='mt-2 min-h-[400px]'>
                    <div className='text-muted-foreground flex h-64 items-center justify-center'>
                        <Code2Icon className='mr-2 h-5 w-5' />
                        功能开发中...
                    </div>
                </TabsContent>
                <TabsContent value='cache' className='mt-2 min-h-[400px]'>
                    <div className='text-muted-foreground flex h-64 items-center justify-center'>
                        <Code2Icon className='mr-2 h-5 w-5' />
                        功能开发中...
                    </div>
                </TabsContent>
                <TabsContent value='sync' className='mt-2 min-h-[400px]'>
                    <div className='text-muted-foreground flex h-64 items-center justify-center'>
                        <Code2Icon className='mr-2 h-5 w-5' />
                        功能开发中...
                    </div>
                </TabsContent>
                <TabsContent value='content' className='mt-2 min-h-[400px]'>
                    <div className='text-muted-foreground flex h-64 items-center justify-center'>
                        <Code2Icon className='mr-2 h-5 w-5' />
                        功能开发中...
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}
