import { useState } from 'react';

import { Edit, PlusIcon, Trash2 } from 'lucide-react';

import { Badge } from '~/components/ui/base/badge';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '~/components/ui/base/table';
import { ActionButton } from '~/components/ui/extras/action-button';
import { PageHeader } from '~/components/ui/extras/page-header';
import { UserAvatar } from '~/components/ui/extras/user-avatar';
import {
    getPermissionColor,
    getPermissionIcon,
    getPermissionLabel,
    mockUserPermissions,
    type UserPermission,
} from '~/types/auth';

export default function UsersAuthPage() {
    const [permissions] = useState<UserPermission[]>(mockUserPermissions);

    return (
        <div className='container mx-auto py-6'>
            <PageHeader
                title='用户授权'
                description='管理用户对项目和域名的访问权限'
            >
                <div className='mt-4'>
                    <ActionButton size='xs' variant='lilith'>
                        <PlusIcon /> 添加授权
                    </ActionButton>
                </div>
            </PageHeader>

            <div className='mt-8'>
                <div className='rounded-md border'>
                    <Table>
                        <TableHeader className='bg-lilith-muted/10 dark:bg-lilith-muted/20'>
                            <TableRow>
                                <TableHead className='pl-8 text-xs'>
                                    用户
                                </TableHead>
                                <TableHead className='text-xs'>权限</TableHead>
                                <TableHead className='text-xs'>
                                    授权时间
                                </TableHead>
                                <TableHead className='text-xs'>
                                    最后更新
                                </TableHead>
                                <TableHead className='pr-8 text-right text-xs'>
                                    操作
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {permissions.map((permission) => (
                                <TableRow
                                    key={`${permission.userId}-${permission.projectId}`}
                                >
                                    <TableCell className='pl-8'>
                                        <div className='flex items-center gap-3'>
                                            <UserAvatar
                                                username={permission.username}
                                                userEmail={permission.userEmail}
                                                userAvatar={
                                                    permission.userAvatar
                                                }
                                                size='sm'
                                            />
                                            <div className='flex flex-col'>
                                                <span className='text-sm font-medium'>
                                                    {permission.username}
                                                </span>
                                                {permission.userEmail && (
                                                    <span className='text-muted-foreground text-xs'>
                                                        {permission.userEmail}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <Badge
                                            className={getPermissionColor(
                                                permission.permission,
                                            )}
                                            variant='outline'
                                        >
                                            {(() => {
                                                const IconComponent =
                                                    getPermissionIcon(
                                                        permission.permission,
                                                    );
                                                return (
                                                    <>
                                                        <IconComponent className='mr-1 h-3 w-3' />
                                                        {getPermissionLabel(
                                                            permission.permission,
                                                        )}
                                                    </>
                                                );
                                            })()}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        <span className='text-xs'>
                                            {new Date(
                                                permission.createdAt,
                                            ).toLocaleString('zh-CN')}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <span className='text-xs'>
                                            {new Date(
                                                permission.updatedAt,
                                            ).toLocaleString('zh-CN')}
                                        </span>
                                    </TableCell>
                                    <TableCell className='pr-4 text-right'>
                                        <ActionButton variant='ghost' size='xs'>
                                            <Edit /> 编辑
                                        </ActionButton>
                                        <ActionButton
                                            variant='ghost'
                                            size='xs'
                                            className='hover:text-lilith-primary text-lilith-primary/70'
                                        >
                                            <Trash2 /> 删除
                                        </ActionButton>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </div>
        </div>
    );
}
