import { useState } from 'react';

import { Edit, Key, Trash2 } from 'lucide-react';

import { ActionButton } from '~/components/ui';
import { Button } from '~/components/ui/base/button';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '~/components/ui/base/table';
import { CopyButton } from '~/components/ui/extras/copy-button';
import { PageHeader } from '~/components/ui/extras/page-header';
import { mockApiKeys, type ApiKey } from '~/types/auth';

export default function ApiKeysPage() {
    const [apiKeys] = useState<ApiKey[]>(mockApiKeys);

    return (
        <div className='container mx-auto py-6'>
            <PageHeader
                title='API Key 管理'
                description='管理访问 MCDN 服务的 API Key'
            >
                <div className='mt-4'>
                    <ActionButton size='xs' variant='lilith'>
                        <Key className='h-1 w-1' /> 新建API Key
                    </ActionButton>
                </div>
            </PageHeader>

            <div className='mt-8'>
                <div className='rounded-md border'>
                    <Table>
                        <TableHeader className='bg-lilith-muted/10 dark:bg-lilith-muted/20'>
                            <TableRow>
                                <TableHead className='pl-8 text-xs'>
                                    名称
                                </TableHead>
                                <TableHead className='text-xs'>
                                    API Key
                                </TableHead>
                                <TableHead className='text-xs'>
                                    创建时间
                                </TableHead>
                                <TableHead className='text-xs'>
                                    过期时间
                                </TableHead>
                                <TableHead className='text-xs'>
                                    最后使用
                                </TableHead>
                                <TableHead className='pr-8 text-right text-xs'>
                                    操作
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {apiKeys.map((apiKey) => (
                                <TableRow key={apiKey.id}>
                                    <TableCell className='pl-4'>
                                        <span className='text-xs font-medium'>
                                            {apiKey.name}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <div className='flex items-center space-x-2'>
                                            <Button
                                                variant='secondary'
                                                size='sm'
                                                asChild
                                            >
                                                <span className='font-mono text-xs font-normal'>
                                                    {apiKey.key}
                                                </span>
                                            </Button>
                                            <CopyButton
                                                text={apiKey.key}
                                                className='h-6 w-6 p-0'
                                                iconSize='h-3 w-3'
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <span className='text-xs'>
                                            {new Date(
                                                apiKey.createdAt,
                                            ).toLocaleString('zh-CN')}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <span className='text-xs'>
                                            {apiKey.expiresAt
                                                ? new Date(
                                                      apiKey.expiresAt,
                                                  ).toLocaleString('zh-CN')
                                                : '永不过期'}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <span className='text-muted-foreground text-xs'>
                                            {apiKey.lastUsedAt
                                                ? new Date(
                                                      apiKey.lastUsedAt,
                                                  ).toLocaleString('zh-CN')
                                                : '从未使用'}
                                        </span>
                                    </TableCell>
                                    <TableCell className='text-right'>
                                        <ActionButton variant='ghost' size='xs'>
                                            <Edit /> 编辑
                                        </ActionButton>
                                        <ActionButton
                                            variant='ghost'
                                            size='xs'
                                            className='hover:text-lilith-primary'
                                        >
                                            <Trash2 /> 删除
                                        </ActionButton>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </div>
        </div>
    );
}
