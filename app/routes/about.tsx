import { Link } from 'react-router';

import { Code, Home } from 'lucide-react';

import { Button } from '~/components/ui/base';

export default function About() {
    return (
        <div className='space-y-6'>
            <div className='space-y-2'>
                <h1 className='text-3xl font-bold tracking-tight'>
                    About This Project
                </h1>
                <p className='text-muted-foreground'>
                    Learn more about this React Router application and its
                    features.
                </p>
            </div>

            <div className='prose prose-gray dark:prose-invert max-w-none'>
                <h2>Overview</h2>
                <p>
                    This is a modern React Router application built with the
                    latest web technologies. It demonstrates how to create a
                    professional-looking documentation site with navigation
                    menus and sidebars.
                </p>

                <h2>Features</h2>
                <ul>
                    <li>Modern React Router v7 setup</li>
                    <li>shadcn/ui component library integration</li>
                    <li>Responsive navigation menu</li>
                    <li>Collapsible sidebar with organized navigation</li>
                    <li>Dark mode support</li>
                    <li>TypeScript support</li>
                    <li>Tailwind CSS styling</li>
                </ul>

                <h2>Technology Stack</h2>
                <ul>
                    <li>
                        <strong>React Router v7</strong> - Modern routing for
                        React applications
                    </li>
                    <li>
                        <strong>shadcn/ui</strong> - Beautiful and accessible UI
                        components
                    </li>
                    <li>
                        <strong>Tailwind CSS</strong> - Utility-first CSS
                        framework
                    </li>
                    <li>
                        <strong>TypeScript</strong> - Type-safe JavaScript
                    </li>
                    <li>
                        <strong>Lucide React</strong> - Beautiful icon library
                    </li>
                </ul>
            </div>

            <div className='flex gap-4'>
                <Button asChild>
                    <Link to='/'>
                        <Home className='mr-2 h-4 w-4' />
                        Back to Home
                    </Link>
                </Button>
                <Button asChild variant='outline'>
                    <Link to='/docs/introduction'>
                        <Code className='mr-2 h-4 w-4' />
                        View Documentation
                    </Link>
                </Button>
            </div>
        </div>
    );
}
