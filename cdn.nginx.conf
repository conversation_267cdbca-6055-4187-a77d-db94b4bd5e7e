# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name lilith-cdn.lilithgame.com;
    return 301 https://$host$request_uri;
}

# HTTPS 主配置
server {
    listen 443 ssl;
    server_name lilith-cdn.lilithgame.com;
    
    # SSL 配置
    ssl_certificate /etc/nginx/ssl/lilithgames.com.pem;
    ssl_certificate_key /etc/nginx/ssl/lilithgames.com.key;
    # ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_session_cache shared:SSL:10m;
    # ssl_session_timeout 10m;

    # 主应用路由
    root /opt/mcdn-web/build/client;

    location / {
        try_files $uri $uri/ /index.html;
    }

    
    # 错误页面
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # 日志配置
    access_log /var/log/nginx/cdn-web.access.log;
    error_log /var/log/nginx/cdn-web.error.log;
}
