import js from '@eslint/js';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsparser from '@typescript-eslint/parser';
import prettier from 'eslint-config-prettier';
import importPlugin from 'eslint-plugin-import';
import unusedImports from 'eslint-plugin-unused-imports';
import { defineConfig } from 'eslint/config';
import globals from 'globals';

export default defineConfig([
    // 全局忽略配置
    {
        ignores: ['.react-router/**', 'build/**', 'node_modules/**'],
    },

    // JavaScript 文件配置
    {
        files: ['**/*.{js,jsx}'],
        languageOptions: {
            globals: {
                ...globals.browser,
                ...globals.node,
                ...globals.es2022,
            },
        },
        plugins: {
            js,
            'unused-imports': unusedImports,
            import: importPlugin,
        },
        extends: ['js/recommended'],
        rules: {
            // 未使用导入和变量规则
            'unused-imports/no-unused-imports': 'error',
            'unused-imports/no-unused-vars': [
                'warn',
                {
                    vars: 'all',
                    varsIgnorePattern: '^_',
                    args: 'after-used',
                    argsIgnorePattern: '^_',
                },
            ],

            // JavaScript 特定规则
            'prefer-const': 'error',
            'no-var': 'error',
            'no-console': 'warn',

            // import 插件规则
            'import/no-duplicates': 'error',
            'import/no-self-import': 'error',
            'import/first': 'error',
            'import/newline-after-import': 'error',
        },
    },

    // TypeScript 文件配置
    {
        files: ['**/*.{ts,tsx}'],
        languageOptions: {
            parser: tsparser,
            parserOptions: {
                ecmaVersion: 'latest',
                sourceType: 'module',
                ecmaFeatures: {
                    jsx: true,
                },
            },
            globals: {
                ...globals.browser,
                ...globals.node,
                ...globals.es2022,
            },
        },
        plugins: {
            '@typescript-eslint': tseslint,
            'unused-imports': unusedImports,
            import: importPlugin,
        },
        extends: ['@typescript-eslint/recommended'],
        rules: {
            // 禁用与 TypeScript 冲突的 JavaScript 规则
            'no-unused-vars': 'off',
            'no-undef': 'off',

            // 未使用导入和变量规则
            'unused-imports/no-unused-imports': 'error',
            'unused-imports/no-unused-vars': [
                'warn',
                {
                    vars: 'all',
                    varsIgnorePattern: '^_',
                    args: 'after-used',
                    argsIgnorePattern: '^_',
                },
            ],

            // TypeScript 特定规则优化
            '@typescript-eslint/no-unused-vars': 'off', // 使用 unused-imports 插件代替
            '@typescript-eslint/no-explicit-any': 'warn',
            '@typescript-eslint/no-non-null-assertion': 'warn',

            // 基础规则
            'prefer-const': 'error',
            'no-var': 'error',
            'no-console': 'warn',

            // React/JSX 相关规则
            '@typescript-eslint/no-empty-interface': [
                'error',
                {
                    allowSingleExtends: true,
                },
            ],

            // import 插件规则
            'import/no-duplicates': 'error',
            'import/no-self-import': 'error',
            'import/no-cycle': 'warn',
            'import/first': 'error',
            'import/newline-after-import': 'error',
            'import/order': [
                'error',
                {
                    groups: [
                        'builtin',
                        'external',
                        'internal',
                        'parent',
                        'sibling',
                        'index',
                    ],
                    'newlines-between': 'always',
                    pathGroups: [
                        {
                            pattern: 'react',
                            group: 'external',
                            position: 'before',
                        },
                        {
                            pattern: 'react-*',
                            group: 'external',
                            position: 'before',
                        },
                    ],
                    pathGroupsExcludedImportTypes: ['react'],
                    alphabetize: {
                        order: 'asc',
                        caseInsensitive: true,
                    },
                },
            ],
        },
        settings: {
            'import/resolver': {
                typescript: {
                    alwaysTryTypes: true,
                    project: './tsconfig.json',
                },
            },
        },
    },

    // Prettier 配置 - 禁用可能与 Prettier 冲突的规则
    prettier,

    // 配置文件忽略规则
    {
        files: ['*.config.{js,mjs,ts}', 'vite.config.ts', 'tailwind.config.ts'],
        rules: {
            'no-console': 'off',
            '@typescript-eslint/no-require-imports': 'off',
        },
    },
]);
