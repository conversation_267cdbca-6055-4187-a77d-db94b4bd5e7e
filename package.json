{"name": "my-react-router-app", "private": true, "type": "module", "scripts": {"build": "react-router build", "clean": "rm -rf build dist node_modules/.cache package-lock.json", "clean:all": "rm -rf build dist .react-router node_modules/.cache package-lock.json", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "lint": "eslint app/", "lint:fix": "eslint app/ --fix"}, "dependencies": {"@iconify/react": "^6.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@react-router/node": "^7.8.1", "@react-router/serve": "^7.8.1", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^5.1.29", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.8.1", "recharts": "^2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@react-router/dev": "^7.8.1", "@tailwindcss/vite": "^4.1.12", "@types/node": "^20.19.11", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "vite": "^7.1.2", "vite-tsconfig-paths": "^5.1.4"}}