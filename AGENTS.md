# AGENTS.md

## 构建/检查/测试命令

```bash
bun run dev          # 启动开发服务器
bun run build        # 生产构建
bun run start        # 启动生产服务器
bun run typecheck    # TypeScript 类型检查
bun run lint         # ESLint 代码检查
bun run lint:fix     # 自动修复 ESLint 错误
```

## 代码风格指南

### 导入规范

- 使用绝对路径导入: `~/components/ui/button`
- 按组排序导入: 内置模块 → 外部依赖 → 内部模块
- React 相关导入放在最前面
- 禁止未使用的导入

### 格式化

- 使用 Prettier 格式化代码 (单引号, 尾随逗号, 4空格缩进)
- Tailwind 类名按插件规则自动排序
- 每行最多100字符

### 类型系统

- 优先使用 TypeScript 类型而非接口
- 使用 `type` 定义联合类型和原始类型别名
- 使用 `interface` 定义对象形状和React组件props
- 明确指定函数返回类型

### 命名约定

- 组件文件使用 kebab-case: `button.tsx`
- 组件函数使用 PascalCase: `function Button()`
- 变量和函数使用 camelCase: `const handleClick`
- 布尔属性使用 is/has 前缀: `isLoading`, `hasError`

### 错误处理

- 使用 TypeScript 严格模式捕获错误
- 避免使用 `any` 类型
- 合理使用类型断言和非空断言操作符

### UI 组件

- 使用 `cn()` 工具函数合并 Tailwind 类名
- 组件应支持 className 属性扩展
- 优先使用 shadcn/ui 组件库

## 项目特定约定

- 使用 Bun 作为包管理器和运行时
- React Router v7 路由配置在 `app/routes.ts`
- 组件组织在 `app/components/` 目录
- 类型定义在 `app/types/` 目录
