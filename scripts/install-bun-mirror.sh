#!/usr/bin/env bash
set -euo pipefail

# 自定义 bun 安装脚本，从镜像源下载
BUN_VERSION=${1:-"1.2.19"}
MIRROR_BASE="https://registry.npmmirror.com/@oven"
INSTALL_DIR="$HOME/.bun"
BIN_DIR="$INSTALL_DIR/bin"
TARGET="linux-x64"

echo "正在从镜像源安装 bun v$BUN_VERSION..."

# 创建安装目录
mkdir -p "$BIN_DIR"

# 从镜像源下载（npm 包格式）
BUN_URL="https://registry.npmmirror.com/@oven/bun-$TARGET/-/bun-$TARGET-$BUN_VERSION.tgz"
echo "下载地址: $BUN_URL"

# 下载
curl -fL "$BUN_URL" -o "$BIN_DIR/bun.tgz" || {
    echo "错误: 无法从镜像源下载 bun"
    echo "尝试的URL: $BUN_URL"
    exit 1
}

# 解压（npm tgz 格式）
cd "$BIN_DIR"
tar -xzf bun.tgz
# npm 包结构是 package/bin/bun
mv package/bin/bun ./
chmod +x bun
rm -rf package bun.tgz

echo "✅ bun 安装成功!"
echo "安装路径: $BIN_DIR/bun"

# 添加到 PATH
if ! grep -q "export PATH=\"$BIN_DIR:\$PATH\"" "$HOME/.zshrc" 2>/dev/null; then
    echo "" >> "$HOME/.zshrc"
    echo "# bun" >> "$HOME/.zshrc"
    echo "export BUN_INSTALL=\"$INSTALL_DIR\"" >> "$HOME/.zshrc"
    echo "export PATH=\"$BIN_DIR:\$PATH\"" >> "$HOME/.zshrc"
    echo "已将 bun 添加到 ~/.zshrc"
fi

echo ""
echo "要立即使用 bun，请运行："
echo "  export PATH=\"$BIN_DIR:\$PATH\""
echo "或重新启动终端"
